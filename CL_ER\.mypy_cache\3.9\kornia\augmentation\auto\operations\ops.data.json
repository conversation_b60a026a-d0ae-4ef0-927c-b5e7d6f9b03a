{".class": "MypyFile", "_fullname": "kornia.augmentation.auto.operations.ops", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AutoContrast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.operations.base.OperationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.ops.AutoContrast", "name": "AutoContrast", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.AutoContrast", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.ops", "mro": ["kornia.augmentation.auto.operations.ops.AutoContrast", "kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "initial_probability", "temperature", "symmetric_megnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.AutoContrast.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "initial_probability", "temperature", "symmetric_megnitude"], "arg_types": ["kornia.augmentation.auto.operations.ops.AutoContrast", "builtins.float", "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AutoContrast", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.ops.AutoContrast.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.ops.AutoContrast", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Brightness": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.operations.base.OperationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.ops.Brightness", "name": "Brightness", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Brightness", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.ops", "mro": ["kornia.augmentation.auto.operations.ops.Brightness", "kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Brightness.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "arg_types": ["kornia.augmentation.auto.operations.ops.Brightness", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Brightness", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.ops.Brightness.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.ops.Brightness", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Contrast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.operations.base.OperationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.ops.Contrast", "name": "Contrast", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Contrast", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.ops", "mro": ["kornia.augmentation.auto.operations.ops.Contrast", "kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Contrast.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "arg_types": ["kornia.augmentation.auto.operations.ops.Contrast", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Contrast", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.ops.Contrast.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.ops.Contrast", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Equalize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.operations.base.OperationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.ops.Equalize", "name": "Equalize", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Equalize", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.ops", "mro": ["kornia.augmentation.auto.operations.ops.Equalize", "kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "initial_probability", "temperature"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Equalize.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "initial_probability", "temperature"], "arg_types": ["kornia.augmentation.auto.operations.ops.Equalize", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Equalize", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.ops.Equalize.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.ops.Equalize", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Gray": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.operations.base.OperationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.ops.Gray", "name": "<PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Gray", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.ops", "mro": ["kornia.augmentation.auto.operations.ops.Gray", "kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "initial_probability", "temperature"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Gray.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "initial_probability", "temperature"], "arg_types": ["kornia.augmentation.auto.operations.ops.Gray", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Gray", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.ops.Gray.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.ops.Gray", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HorizontalFlip": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.operations.base.OperationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.ops.HorizontalFlip", "name": "HorizontalFlip", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.HorizontalFlip", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.ops", "mro": ["kornia.augmentation.auto.operations.ops.HorizontalFlip", "kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "initial_probability", "temperature"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.HorizontalFlip.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "initial_probability", "temperature"], "arg_types": ["kornia.augmentation.auto.operations.ops.HorizontalFlip", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HorizontalFlip", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.ops.HorizontalFlip.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.ops.HorizontalFlip", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Hue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.operations.base.OperationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.ops.Hue", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Hue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.ops", "mro": ["kornia.augmentation.auto.operations.ops.Hue", "kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Hue.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "arg_types": ["kornia.augmentation.auto.operations.ops.Hue", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.ops.Hue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.ops.Hue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Invert": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.operations.base.OperationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.ops.Invert", "name": "Invert", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Invert", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.ops", "mro": ["kornia.augmentation.auto.operations.ops.Invert", "kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "initial_probability", "temperature"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Invert.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "initial_probability", "temperature"], "arg_types": ["kornia.augmentation.auto.operations.ops.Invert", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Invert", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.ops.Invert.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.ops.Invert", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "K": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation", "kind": "Gdef", "module_public": false}, "OperationBase": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.base.OperationBase", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Posterize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.operations.base.OperationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.ops.Posterize", "name": "Post<PERSON>ze", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Posterize", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.ops", "mro": ["kornia.augmentation.auto.operations.ops.Posterize", "kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Posterize.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "arg_types": ["kornia.augmentation.auto.operations.ops.Posterize", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Posterize", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_process_magnitude": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["magnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "kornia.augmentation.auto.operations.ops.Posterize._process_magnitude", "name": "_process_magnitude", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["magnitude"], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_magnitude of Posterize", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "kornia.augmentation.auto.operations.ops.Posterize._process_magnitude", "name": "_process_magnitude", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["magnitude"], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_magnitude of Posterize", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.ops.Posterize.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.ops.Posterize", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Rotate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.operations.base.OperationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.ops.Rotate", "name": "Rotate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Rotate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.ops", "mro": ["kornia.augmentation.auto.operations.ops.Rotate", "kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Rotate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "arg_types": ["kornia.augmentation.auto.operations.ops.Rotate", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Rotate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.ops.Rotate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.ops.Rotate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "STEFunction": {".class": "SymbolTableNode", "cross_ref": "kornia.grad_estimator.ste.STEFunction", "kind": "Gdef", "module_public": false}, "Saturate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.operations.base.OperationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.ops.Saturate", "name": "Saturate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Saturate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.ops", "mro": ["kornia.augmentation.auto.operations.ops.Saturate", "kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Saturate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "arg_types": ["kornia.augmentation.auto.operations.ops.Saturate", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Saturate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.ops.Saturate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.ops.Saturate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sharpness": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.operations.base.OperationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.ops.Sharpness", "name": "Sharpness", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Sharpness", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.ops", "mro": ["kornia.augmentation.auto.operations.ops.Sharpness", "kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Sharpness.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "arg_types": ["kornia.augmentation.auto.operations.ops.Sharpness", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Sharpness", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.ops.Sharpness.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.ops.Sharpness", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShearX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.operations.base.OperationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.ops.ShearX", "name": "ShearX", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.ShearX", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.ops", "mro": ["kornia.augmentation.auto.operations.ops.ShearX", "kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.ShearX.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "arg_types": ["kornia.augmentation.auto.operations.ops.ShearX", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ShearX", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_process_magnitude": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["magnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "kornia.augmentation.auto.operations.ops.ShearX._process_magnitude", "name": "_process_magnitude", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["magnitude"], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_magnitude of ShearX", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "kornia.augmentation.auto.operations.ops.ShearX._process_magnitude", "name": "_process_magnitude", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["magnitude"], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_magnitude of ShearX", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.ops.ShearX.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.ops.ShearX", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShearY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.operations.base.OperationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.ops.ShearY", "name": "ShearY", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.ShearY", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.ops", "mro": ["kornia.augmentation.auto.operations.ops.ShearY", "kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.ShearY.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "arg_types": ["kornia.augmentation.auto.operations.ops.ShearY", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ShearY", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_process_magnitude": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["magnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "kornia.augmentation.auto.operations.ops.ShearY._process_magnitude", "name": "_process_magnitude", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["magnitude"], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_magnitude of ShearY", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "kornia.augmentation.auto.operations.ops.ShearY._process_magnitude", "name": "_process_magnitude", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["magnitude"], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_magnitude of ShearY", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.ops.ShearY.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.ops.ShearY", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Solarize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.operations.base.OperationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.ops.Solarize", "name": "Solarize", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Solarize", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.ops", "mro": ["kornia.augmentation.auto.operations.ops.Solarize", "kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.Solarize.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "arg_types": ["kornia.augmentation.auto.operations.ops.Solarize", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Solarize", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.ops.Solarize.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.ops.Solarize", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SolarizeAdd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.operations.base.OperationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.ops.SolarizeAdd", "name": "SolarizeAdd", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.SolarizeAdd", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.ops", "mro": ["kornia.augmentation.auto.operations.ops.SolarizeAdd", "kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.SolarizeAdd.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "arg_types": ["kornia.augmentation.auto.operations.ops.SolarizeAdd", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SolarizeAdd", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.ops.SolarizeAdd.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.ops.SolarizeAdd", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tensor": {".class": "SymbolTableNode", "cross_ref": "kornia.core._backend.Tensor", "kind": "Gdef", "module_public": false}, "TranslateX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.operations.base.OperationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.ops.TranslateX", "name": "TranslateX", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.TranslateX", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.ops", "mro": ["kornia.augmentation.auto.operations.ops.TranslateX", "kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.TranslateX.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "arg_types": ["kornia.augmentation.auto.operations.ops.TranslateX", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TranslateX", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.ops.TranslateX.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.ops.TranslateX", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TranslateY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.operations.base.OperationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.ops.TranslateY", "name": "TranslateY", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.TranslateY", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.ops", "mro": ["kornia.augmentation.auto.operations.ops.TranslateY", "kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.TranslateY.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "initial_magnitude", "initial_probability", "magnitude_range", "temperature", "symmetric_megnitude"], "arg_types": ["kornia.augmentation.auto.operations.ops.TranslateY", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TranslateY", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.ops.TranslateY.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.ops.TranslateY", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "VerticalFlip": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.operations.base.OperationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.ops.VerticalFlip", "name": "VerticalFlip", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.VerticalFlip", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.ops", "mro": ["kornia.augmentation.auto.operations.ops.VerticalFlip", "kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "initial_probability", "temperature"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.ops.VerticalFlip.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "initial_probability", "temperature"], "arg_types": ["kornia.augmentation.auto.operations.ops.VerticalFlip", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of VerticalFlip", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.ops.VerticalFlip.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.ops.VerticalFlip", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "kornia.augmentation.auto.operations.ops.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.ops.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.ops.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.ops.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.ops.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.ops.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.ops.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\kornia\\augmentation\\auto\\operations\\ops.py"}