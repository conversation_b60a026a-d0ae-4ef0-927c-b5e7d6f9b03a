{"data_mtime": 1754286450, "dep_lines": [3, 893, 2, 5, 1, 4, 6, 891, 894, 928, 1026, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 875], "dep_prios": [10, 20, 10, 5, 10, 10, 10, 20, 20, 20, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["torch.nn.functional", "torch.utils.data", "torch.nn", "agents.exp_replay", "torch", "numpy", "copy", "pickle", "os", "traceback", "gc", "builtins", "string", "torch.distributed._functional_collectives", "types", "itertools", "contextlib", "torch.distributed", "html", "operator", "re", "sys", "typing", "multiprocessing.reduction", "collections", "warnings", "pprint", "inspect", "math", "uuid", "_frozen_importlib", "_typeshed", "abc", "agents.base", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.module", "torch.types"], "hash": "e9c3172b8e255006be19aeddb7b5d9fa4e115ac1", "id": "agents.hybrid", "ignore_all": true, "interface_hash": "04e9bc78c9e219b786ab46d2dd2e7a86f983a5d1", "mtime": 1753114029, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\agents\\hybrid.py", "plugin_data": null, "size": 45656, "suppressed": ["sklearn.metrics"], "version_id": "1.15.0"}