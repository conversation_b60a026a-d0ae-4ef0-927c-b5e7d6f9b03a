{".class": "MypyFile", "_fullname": "errno", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "E2BIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.E2BIG", "name": "E2BIG", "type": "builtins.int"}}, "EACCES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EACCES", "name": "EACCES", "type": "builtins.int"}}, "EADDRINUSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EADDRINUSE", "name": "EADDRINUSE", "type": "builtins.int"}}, "EADDRNOTAVAIL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EADDRNOTAVAIL", "name": "EADDRNOTAVAIL", "type": "builtins.int"}}, "EAFNOSUPPORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EAFNOSUPPORT", "name": "EAFNOSUPPORT", "type": "builtins.int"}}, "EAGAIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EAGAIN", "name": "EAGAIN", "type": "builtins.int"}}, "EALREADY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EALREADY", "name": "EALREADY", "type": "builtins.int"}}, "EBADF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EBADF", "name": "EBADF", "type": "builtins.int"}}, "EBADMSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EBADMSG", "name": "EBADMSG", "type": "builtins.int"}}, "EBUSY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EBUSY", "name": "EBUSY", "type": "builtins.int"}}, "ECANCELED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ECANCELED", "name": "ECANCELED", "type": "builtins.int"}}, "ECHILD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ECHILD", "name": "ECHILD", "type": "builtins.int"}}, "ECONNABORTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ECONNABORTED", "name": "ECONNABORTED", "type": "builtins.int"}}, "ECONNREFUSED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ECONNREFUSED", "name": "ECONNREFUSED", "type": "builtins.int"}}, "ECONNRESET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ECONNRESET", "name": "ECONNRESET", "type": "builtins.int"}}, "EDEADLK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EDEADLK", "name": "EDEADLK", "type": "builtins.int"}}, "EDEADLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EDEADLOCK", "name": "EDEADLOCK", "type": "builtins.int"}}, "EDESTADDRREQ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EDESTADDRREQ", "name": "EDESTADDRREQ", "type": "builtins.int"}}, "EDOM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EDOM", "name": "EDOM", "type": "builtins.int"}}, "EDQUOT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EDQUOT", "name": "EDQUOT", "type": "builtins.int"}}, "EEXIST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EEXIST", "name": "EEXIST", "type": "builtins.int"}}, "EFAULT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EFAULT", "name": "EFAULT", "type": "builtins.int"}}, "EFBIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EFBIG", "name": "EFBIG", "type": "builtins.int"}}, "EHOSTDOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EHOSTDOWN", "name": "EHOSTDOWN", "type": "builtins.int"}}, "EHOSTUNREACH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EHOSTUNREACH", "name": "EHOSTUNREACH", "type": "builtins.int"}}, "EIDRM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EIDRM", "name": "EIDRM", "type": "builtins.int"}}, "EILSEQ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EILSEQ", "name": "EILSEQ", "type": "builtins.int"}}, "EINPROGRESS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EINPROGRESS", "name": "EINPROGRESS", "type": "builtins.int"}}, "EINTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EINTR", "name": "EINTR", "type": "builtins.int"}}, "EINVAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EINVAL", "name": "EINVAL", "type": "builtins.int"}}, "EIO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EIO", "name": "EIO", "type": "builtins.int"}}, "EISCONN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EISCONN", "name": "EISCONN", "type": "builtins.int"}}, "EISDIR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EISDIR", "name": "EISDIR", "type": "builtins.int"}}, "ELOOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ELOOP", "name": "ELOOP", "type": "builtins.int"}}, "EMFILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EMFILE", "name": "EMFILE", "type": "builtins.int"}}, "EMLINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EMLINK", "name": "EMLINK", "type": "builtins.int"}}, "EMSGSIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EMSGSIZE", "name": "EMSGSIZE", "type": "builtins.int"}}, "ENAMETOOLONG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENAMETOOLONG", "name": "ENAMETOOLONG", "type": "builtins.int"}}, "ENETDOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENETDOWN", "name": "ENETDOWN", "type": "builtins.int"}}, "ENETRESET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENETRESET", "name": "ENETRESET", "type": "builtins.int"}}, "ENETUNREACH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENETUNREACH", "name": "ENETUNREACH", "type": "builtins.int"}}, "ENFILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENFILE", "name": "ENFILE", "type": "builtins.int"}}, "ENOBUFS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOBUFS", "name": "ENOBUFS", "type": "builtins.int"}}, "ENODATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENODATA", "name": "ENODATA", "type": "builtins.int"}}, "ENODEV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENODEV", "name": "ENODEV", "type": "builtins.int"}}, "ENOENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOENT", "name": "ENOENT", "type": "builtins.int"}}, "ENOEXEC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOEXEC", "name": "ENOEXEC", "type": "builtins.int"}}, "ENOLCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOLCK", "name": "ENOLCK", "type": "builtins.int"}}, "ENOLINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOLINK", "name": "ENOLINK", "type": "builtins.int"}}, "ENOMEM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOMEM", "name": "ENOMEM", "type": "builtins.int"}}, "ENOMSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOMSG", "name": "ENOMSG", "type": "builtins.int"}}, "ENOPROTOOPT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOPROTOOPT", "name": "ENOPROTOOPT", "type": "builtins.int"}}, "ENOSPC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOSPC", "name": "ENOSPC", "type": "builtins.int"}}, "ENOSR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOSR", "name": "ENOSR", "type": "builtins.int"}}, "ENOSTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOSTR", "name": "ENOSTR", "type": "builtins.int"}}, "ENOSYS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOSYS", "name": "ENOSYS", "type": "builtins.int"}}, "ENOTCONN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTCONN", "name": "ENOTCONN", "type": "builtins.int"}}, "ENOTDIR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTDIR", "name": "ENOTDIR", "type": "builtins.int"}}, "ENOTEMPTY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTEMPTY", "name": "ENOTEMPTY", "type": "builtins.int"}}, "ENOTRECOVERABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTRECOVERABLE", "name": "ENOTRECOVERABLE", "type": "builtins.int"}}, "ENOTSOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTSOCK", "name": "ENOTSOCK", "type": "builtins.int"}}, "ENOTSUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTSUP", "name": "ENOTSUP", "type": "builtins.int"}}, "ENOTTY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTTY", "name": "ENOTTY", "type": "builtins.int"}}, "ENXIO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENXIO", "name": "ENXIO", "type": "builtins.int"}}, "EOPNOTSUPP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EOPNOTSUPP", "name": "EOPNOTSUPP", "type": "builtins.int"}}, "EOVERFLOW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EOVERFLOW", "name": "EOVERFLOW", "type": "builtins.int"}}, "EOWNERDEAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EOWNERDEAD", "name": "EOWNERDEAD", "type": "builtins.int"}}, "EPERM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPERM", "name": "EPERM", "type": "builtins.int"}}, "EPFNOSUPPORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPFNOSUPPORT", "name": "EPFNOSUPPORT", "type": "builtins.int"}}, "EPIPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPIPE", "name": "EPIPE", "type": "builtins.int"}}, "EPROTO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPROTO", "name": "EPROTO", "type": "builtins.int"}}, "EPROTONOSUPPORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPROTONOSUPPORT", "name": "EPROTONOSUPPORT", "type": "builtins.int"}}, "EPROTOTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPROTOTYPE", "name": "EPROTOTYPE", "type": "builtins.int"}}, "ERANGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ERANGE", "name": "ERANGE", "type": "builtins.int"}}, "EREMOTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EREMOTE", "name": "EREMOTE", "type": "builtins.int"}}, "EROFS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EROFS", "name": "EROFS", "type": "builtins.int"}}, "ESHUTDOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ESHUTDOWN", "name": "ESHUTDOWN", "type": "builtins.int"}}, "ESOCKTNOSUPPORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ESOCKTNOSUPPORT", "name": "ESOCKTNOSUPPORT", "type": "builtins.int"}}, "ESPIPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ESPIPE", "name": "ESPIPE", "type": "builtins.int"}}, "ESRCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ESRCH", "name": "ESRCH", "type": "builtins.int"}}, "ESTALE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ESTALE", "name": "ESTALE", "type": "builtins.int"}}, "ETIME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ETIME", "name": "ETIME", "type": "builtins.int"}}, "ETIMEDOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ETIMEDOUT", "name": "ETIMEDOUT", "type": "builtins.int"}}, "ETOOMANYREFS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ETOOMANYREFS", "name": "ETOOMANYREFS", "type": "builtins.int"}}, "ETXTBSY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ETXTBSY", "name": "ETXTBSY", "type": "builtins.int"}}, "EUSERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EUSERS", "name": "EUSERS", "type": "builtins.int"}}, "EWOULDBLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EWOULDBLOCK", "name": "EWOULDBLOCK", "type": "builtins.int"}}, "EXDEV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EXDEV", "name": "EXDEV", "type": "builtins.int"}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "WSABASEERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSABASEERR", "name": "WSABASEERR", "type": "builtins.int"}}, "WSAEACCES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEACCES", "name": "WSAEACCES", "type": "builtins.int"}}, "WSAEADDRINUSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEADDRINUSE", "name": "WSAEADDRINUSE", "type": "builtins.int"}}, "WSAEADDRNOTAVAIL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEADDRNOTAVAIL", "name": "WSAEADDRNOTAVAIL", "type": "builtins.int"}}, "WSAEAFNOSUPPORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEAFNOSUPPORT", "name": "WSAEAFNOSUPPORT", "type": "builtins.int"}}, "WSAEALREADY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEALREADY", "name": "WSAEALREADY", "type": "builtins.int"}}, "WSAEBADF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEBADF", "name": "WSAEBADF", "type": "builtins.int"}}, "WSAECONNABORTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAECONNABORTED", "name": "WSAECONNABORTED", "type": "builtins.int"}}, "WSAECONNREFUSED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAECONNREFUSED", "name": "WSAECONNREFUSED", "type": "builtins.int"}}, "WSAECONNRESET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAECONNRESET", "name": "WSAECONNRESET", "type": "builtins.int"}}, "WSAEDESTADDRREQ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEDESTADDRREQ", "name": "WSAEDESTADDRREQ", "type": "builtins.int"}}, "WSAEDISCON": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEDISCON", "name": "WSAEDISCON", "type": "builtins.int"}}, "WSAEDQUOT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEDQUOT", "name": "WSAEDQUOT", "type": "builtins.int"}}, "WSAEFAULT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEFAULT", "name": "WSAEFAULT", "type": "builtins.int"}}, "WSAEHOSTDOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEHOSTDOWN", "name": "WSAEHOSTDOWN", "type": "builtins.int"}}, "WSAEHOSTUNREACH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEHOSTUNREACH", "name": "WSAEHOSTUNREACH", "type": "builtins.int"}}, "WSAEINPROGRESS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEINPROGRESS", "name": "WSAEINPROGRESS", "type": "builtins.int"}}, "WSAEINTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEINTR", "name": "WSAEINTR", "type": "builtins.int"}}, "WSAEINVAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEINVAL", "name": "WSAEINVAL", "type": "builtins.int"}}, "WSAEISCONN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEISCONN", "name": "WSAEISCONN", "type": "builtins.int"}}, "WSAELOOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAELOOP", "name": "WSAELOOP", "type": "builtins.int"}}, "WSAEMFILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEMFILE", "name": "WSAEMFILE", "type": "builtins.int"}}, "WSAEMSGSIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEMSGSIZE", "name": "WSAEMSGSIZE", "type": "builtins.int"}}, "WSAENAMETOOLONG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAENAMETOOLONG", "name": "WSAENAMETOOLONG", "type": "builtins.int"}}, "WSAENETDOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAENETDOWN", "name": "WSAENETDOWN", "type": "builtins.int"}}, "WSAENETRESET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAENETRESET", "name": "WSAENETRESET", "type": "builtins.int"}}, "WSAENETUNREACH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "er<PERSON>.WSAENETUNREACH", "name": "WSAENETUNREACH", "type": "builtins.int"}}, "WSAENOBUFS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAENOBUFS", "name": "WSAENOBUFS", "type": "builtins.int"}}, "WSAENOPROTOOPT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAENOPROTOOPT", "name": "WSAENOPROTOOPT", "type": "builtins.int"}}, "WSAENOTCONN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAENOTCONN", "name": "WSAENOTCONN", "type": "builtins.int"}}, "WSAENOTEMPTY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAENOTEMPTY", "name": "WSAENOTEMPTY", "type": "builtins.int"}}, "WSAENOTSOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAENOTSOCK", "name": "WSAENOTSOCK", "type": "builtins.int"}}, "WSAEOPNOTSUPP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEOPNOTSUPP", "name": "WSAEOPNOTSUPP", "type": "builtins.int"}}, "WSAEPFNOSUPPORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEPFNOSUPPORT", "name": "WSAEPFNOSUPPORT", "type": "builtins.int"}}, "WSAEPROCLIM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEPROCLIM", "name": "WSAEPROCLIM", "type": "builtins.int"}}, "WSAEPROTONOSUPPORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEPROTONOSUPPORT", "name": "WSAEPROTONOSUPPORT", "type": "builtins.int"}}, "WSAEPROTOTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEPROTOTYPE", "name": "WSAEPROTOTYPE", "type": "builtins.int"}}, "WSAEREMOTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEREMOTE", "name": "WSAEREMOTE", "type": "builtins.int"}}, "WSAESHUTDOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAESHUTDOWN", "name": "WSAESHUTDOWN", "type": "builtins.int"}}, "WSAESOCKTNOSUPPORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAESOCKTNOSUPPORT", "name": "WSAESOCKTNOSUPPORT", "type": "builtins.int"}}, "WSAESTALE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAESTALE", "name": "WSAESTALE", "type": "builtins.int"}}, "WSAETIMEDOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAETIMEDOUT", "name": "WSAETIMEDOUT", "type": "builtins.int"}}, "WSAETOOMANYREFS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAETOOMANYREFS", "name": "WSAETOOMANYREFS", "type": "builtins.int"}}, "WSAEUSERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEUSERS", "name": "WSAEUSERS", "type": "builtins.int"}}, "WSAEWOULDBLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAEWOULDBLOCK", "name": "WSAEWOULDBLOCK", "type": "builtins.int"}}, "WSANOTINITIALISED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSANOTINITIALISED", "name": "WSANOTINITIALISED", "type": "builtins.int"}}, "WSASYSNOTREADY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSASYSNOTREADY", "name": "WSASYSNOTREADY", "type": "builtins.int"}}, "WSAVERNOTSUPPORTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.WSAVERNOTSUPPORTED", "name": "WSAVERNOTSUPPORTED", "type": "builtins.int"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "errorcode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.errorcode", "name": "errorcode", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\errno.pyi"}