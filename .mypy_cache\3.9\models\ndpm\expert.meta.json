{"data_mtime": 1754288103, "dep_lines": [3, 4, 1, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["models.ndpm.classifier", "models.ndpm.vae", "torch.nn", "utils.utils", "utils.global_vars", "torch", "builtins", "_frozen_importlib", "abc", "models.ndpm.component", "torch.nn.modules", "torch.nn.modules.module", "typing", "utils"], "hash": "8108a29279c76f95f17861be78df54dd346aabd8", "id": "models.ndpm.expert", "ignore_all": true, "interface_hash": "3bad89a5b7abba11f633aaa7a471b58f92842afc", "mtime": 1751894924, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\models\\ndpm\\expert.py", "plugin_data": null, "size": 1997, "suppressed": [], "version_id": "1.15.0"}