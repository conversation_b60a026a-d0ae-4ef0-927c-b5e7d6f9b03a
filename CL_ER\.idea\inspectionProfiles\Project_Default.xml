<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyInterpreterInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="20">
            <item index="0" class="java.lang.String" itemvalue="librosa" />
            <item index="1" class="java.lang.String" itemvalue="onedrivedownloader" />
            <item index="2" class="java.lang.String" itemvalue="tqdm" />
            <item index="3" class="java.lang.String" itemvalue="av" />
            <item index="4" class="java.lang.String" itemvalue="wandb" />
            <item index="5" class="java.lang.String" itemvalue="torch" />
            <item index="6" class="java.lang.String" itemvalue="torchvision" />
            <item index="7" class="java.lang.String" itemvalue="setproctitle" />
            <item index="8" class="java.lang.String" itemvalue="six" />
            <item index="9" class="java.lang.String" itemvalue="numpy" />
            <item index="10" class="java.lang.String" itemvalue="f175795ac5baee6e18ffcd529393f1a6915b8fa2" />
            <item index="11" class="java.lang.String" itemvalue="scikit-image" />
            <item index="12" class="java.lang.String" itemvalue="pandas" />
            <item index="13" class="java.lang.String" itemvalue="kornia" />
            <item index="14" class="java.lang.String" itemvalue="scipy" />
            <item index="15" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="16" class="java.lang.String" itemvalue="PyYAML" />
            <item index="17" class="java.lang.String" itemvalue="matplotlib" />
            <item index="18" class="java.lang.String" itemvalue="psutil" />
            <item index="19" class="java.lang.String" itemvalue="pytorch" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>