{"data_mtime": 1754286450, "dep_lines": [4, 6, 8, 10, 1, 2, 3, 5, 6, 8, 3, 7, 11, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 5, 20, 20, 20, 10, 10, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["utils.buffer.buffer_utils", "torch.utils.data", "torch.nn.functional", "utils.buffer.buffer", "agents.base", "continuum.data_utils", "utils.utils", "utils.setup_elements", "torch.utils", "torch.nn", "utils", "numpy", "torch", "copy", "builtins", "string", "torch.distributed._functional_collectives", "types", "itertools", "contextlib", "traceback", "torch.distributed", "html", "operator", "os", "re", "sys", "typing", "multiprocessing.reduction", "collections", "warnings", "pprint", "inspect", "math", "uuid", "_frozen_importlib", "abc", "continuum", "torch.nn.modules", "torch.nn.modules.module", "torch.utils.data.dataloader", "torch.utils.data.dataset", "torch.utils.data.sampler", "utils.buffer"], "hash": "bc5760ce70246d3bfdba541201befef45bec88b4", "id": "agents.icarl", "ignore_all": true, "interface_hash": "d116c8f6f8d02836fbed8c2543e98e9a6e79489b", "mtime": 1751894900, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\agents\\icarl.py", "plugin_data": null, "size": 3905, "suppressed": [], "version_id": "1.15.0"}