{".class": "MypyFile", "_fullname": "agents.noise_injection", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ExperienceReplay": {".class": "SymbolTableNode", "cross_ref": "agents.exp_replay.ExperienceReplay", "kind": "Gdef"}, "F": {".class": "SymbolTableNode", "cross_ref": "torch.nn.functional", "kind": "Gdef"}, "NoiseInjectionAgent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["agents.exp_replay.ExperienceReplay"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "agents.noise_injection.NoiseInjectionAgent", "name": "NoiseInjectionAgent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "agents.noise_injection.NoiseInjectionAgent", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "agents.noise_injection", "mro": ["agents.noise_injection.NoiseInjectionAgent", "agents.exp_replay.ExperienceReplay", "agents.base.ContinualLearner", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "optimizer", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.noise_injection.NoiseInjectionAgent.__init__", "name": "__init__", "type": null}}, "_get_task_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.noise_injection.NoiseInjectionAgent._get_task_data", "name": "_get_task_data", "type": null}}, "_identify_sensitive_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "forget_task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.noise_injection.NoiseInjectionAgent._identify_sensitive_params", "name": "_identify_sensitive_params", "type": null}}, "_inject_noise": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sensitive_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.noise_injection.NoiseInjectionAgent._inject_noise", "name": "_inject_noise", "type": null}}, "_noise_injection_unlearning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "forget_task_id", "retain_x", "retain_y", "original_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.noise_injection.NoiseInjectionAgent._noise_injection_unlearning", "name": "_noise_injection_unlearning", "type": null}}, "_remove_task_from_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.noise_injection.NoiseInjectionAgent._remove_task_from_buffer", "name": "_remove_task_from_buffer", "type": null}}, "evaluate_accuracy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.noise_injection.NoiseInjectionAgent.evaluate_accuracy", "name": "evaluate_accuracy", "type": null}}, "evaluate_membership_inference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "forget_x", "forget_y", "retain_x", "retain_y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.noise_injection.NoiseInjectionAgent.evaluate_membership_inference", "name": "evaluate_membership_inference", "type": null}}, "forget": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "forget_task_id", "subjects_to_keep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.noise_injection.NoiseInjectionAgent.forget", "name": "forget", "type": null}}, "model_cls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.noise_injection.NoiseInjectionAgent.model_cls", "name": "model_cls", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "model_encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.noise_injection.NoiseInjectionAgent.model_encoder", "name": "model_encoder", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "noise_scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.noise_injection.NoiseInjectionAgent.noise_scale", "name": "noise_scale", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "unlearning_epochs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.noise_injection.NoiseInjectionAgent.unlearning_epochs", "name": "unlearning_epochs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "agents.noise_injection.NoiseInjectionAgent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "agents.noise_injection.NoiseInjectionAgent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.noise_injection.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.noise_injection.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.noise_injection.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.noise_injection.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.noise_injection.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.noise_injection.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "D:\\continual_learning\\CL_ER\\agents\\noise_injection.py"}