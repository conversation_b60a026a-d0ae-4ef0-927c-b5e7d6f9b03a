{".class": "MypyFile", "_fullname": "Contrastive_loss", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "SupConLoss": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Contrastive_loss.SupConLoss", "name": "SupConLoss", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Contrastive_loss.SupConLoss", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Contrastive_loss", "mro": ["Contrastive_loss.SupConLoss", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "temperature"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Contrastive_loss.SupConLoss.__init__", "name": "__init__", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "features", "labels", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Contrastive_loss.SupConLoss.forward", "name": "forward", "type": null}}, "temperature": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "Contrastive_loss.SupConLoss.temperature", "name": "temperature", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Contrastive_loss.SupConLoss.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Contrastive_loss.SupConLoss", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Contrastive_loss.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Contrastive_loss.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Contrastive_loss.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Contrastive_loss.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Contrastive_loss.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Contrastive_loss.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "nn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Contrastive_loss.nn", "name": "nn", "type": {".class": "AnyType", "missing_import_name": "Contrastive_loss.nn", "source_any": null, "type_of_any": 3}}}, "torch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Contrastive_loss.torch", "name": "torch", "type": {".class": "AnyType", "missing_import_name": "Contrastive_loss.torch", "source_any": null, "type_of_any": 3}}}}, "path": "D:\\continual_learning\\CL_ER\\Contrastive_loss.py"}