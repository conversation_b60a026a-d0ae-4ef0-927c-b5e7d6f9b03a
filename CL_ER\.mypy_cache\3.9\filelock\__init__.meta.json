{"data_mtime": 1754286349, "dep_lines": [14, 15, 16, 17, 18, 19, 8, 10, 11, 12, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 30, 30, 30, 30], "dependencies": ["filelock._api", "filelock._error", "filelock._soft", "filelock._unix", "filelock._windows", "filelock.version", "__future__", "sys", "warnings", "typing", "builtins", "_frozen_importlib", "abc", "contextlib", "os"], "hash": "332560cc61a945a647976c82bdacb6739f2de32f", "id": "filelock", "ignore_all": true, "interface_hash": "d0dafd6bb14e11dcab6fc8b28d22386191430b96", "mtime": 1700591336, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\filelock\\__init__.py", "plugin_data": null, "size": 1213, "suppressed": [], "version_id": "1.15.0"}