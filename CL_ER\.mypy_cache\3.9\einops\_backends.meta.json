{"data_mtime": 1754286427, "dep_lines": [283, 411, 471, 538, 604, 222, 283, 13, 161, 218, 222, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 203, 203, 295, 353, 480, 550, 619, 672], "dep_prios": [20, 20, 20, 20, 20, 20, 20, 10, 20, 20, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 20, 20, 20, 20, 20, 20, 20, 20], "dependencies": ["einops.layers.torch", "einops.layers.tensorflow", "einops.layers.keras", "einops.layers.oneflow", "einops.layers.paddle", "einops._torch_specific", "einops.layers", "sys", "numpy", "torch", "einops", "builtins", "string", "torch.distributed._functional_collectives", "types", "itertools", "contextlib", "traceback", "torch.nn", "html", "operator", "os", "re", "typing", "multiprocessing.reduction", "collections", "warnings", "pprint", "inspect", "copy", "math", "_frozen_importlib", "abc"], "hash": "c321110964c13d2208c038eef0f4e613e5e3f5dd", "id": "einops._backends", "ignore_all": true, "interface_hash": "f3be83aedb9a8661b4d34a45d7dfa1e1aabf2355", "mtime": 1740554970, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\einops\\_backends.py", "plugin_data": null, "size": 21281, "suppressed": ["jax.numpy", "jax", "cupy", "tensorflow", "oneflow", "paddle", "tinygrad", "pytensor"], "version_id": "1.15.0"}