{"data_mtime": 1754286348, "dep_lines": [6, 12, 1, 2, 3, 4, 5, 6, 8, 10, 11, 14, 17, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 25, 5, 30, 30, 30, 30], "dependencies": ["collections.abc", "urllib.parse", "enum", "json", "os", "re", "typing", "collections", "random", "threading", "types", "markupsafe", "typing_extensions", "builtins", "_frozen_importlib", "_thread", "_typeshed", "abc"], "hash": "a8fbc9e696bbdacd4ec1673b5e1f43a378983a4a", "id": "jinja2.utils", "ignore_all": true, "interface_hash": "0d0284767de093bbb2c283bbdac6878900a9c72c", "mtime": 1716993775, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\jinja2\\utils.py", "plugin_data": null, "size": 23952, "suppressed": [], "version_id": "1.15.0"}