{".class": "MypyFile", "_fullname": "einops._backends", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops._backends.AbstractBackend", "name": "AbstractBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops._backends", "mro": ["einops._backends.AbstractBackend", "builtins.object"], "names": {".class": "SymbolTable", "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend.__repr__", "name": "__repr__", "type": null}}, "add_axes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "x", "n_axes", "pos2len"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend.add_axes", "name": "add_axes", "type": null}}, "add_axis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "new_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend.add_axis", "name": "add_axis", "type": null}}, "arange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "start", "stop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend.arange", "name": "arange", "type": null}}, "concat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend.concat", "name": "concat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "arg_types": ["einops._backends.AbstractBackend", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concat of AbstractBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_symbol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend.create_symbol", "name": "create_symbol", "type": null}}, "einsum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "pattern", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend.einsum", "name": "einsum", "type": null}}, "eval_symbol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "symbol", "symbol_value_pairs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend.eval_symbol", "name": "eval_symbol", "type": null}}, "framework_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "einops._backends.AbstractBackend.framework_name", "name": "framework_name", "type": "builtins.str"}}, "from_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend.from_numpy", "name": "from_numpy", "type": null}}, "is_appropriate_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend.is_appropriate_type", "name": "is_appropriate_type", "type": null}}, "is_float_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend.is_float_type", "name": "is_float_type", "type": null}}, "layers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend.layers", "name": "layers", "type": null}}, "reduce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "x", "operation", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend.reduce", "name": "reduce", "type": null}}, "reshape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend.reshape", "name": "reshape", "type": null}}, "shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend.shape", "name": "shape", "type": null}}, "stack_on_zeroth_dimension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend.stack_on_zeroth_dimension", "name": "stack_on_zeroth_dimension", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "arg_types": ["einops._backends.AbstractBackend", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_on_zeroth_dimension of AbstractBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "repeats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend.tile", "name": "tile", "type": null}}, "to_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend.to_numpy", "name": "to_numpy", "type": null}}, "transpose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.AbstractBackend.transpose", "name": "transpose", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops._backends.AbstractBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops._backends.AbstractBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CupyBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["einops._backends.AbstractBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops._backends.CupyBackend", "name": "CupyBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "einops._backends.CupyBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops._backends", "mro": ["einops._backends.CupyBackend", "einops._backends.AbstractBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.CupyBackend.__init__", "name": "__init__", "type": null}}, "add_axis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "new_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.CupyBackend.add_axis", "name": "add_axis", "type": null}}, "arange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "start", "stop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.CupyBackend.arange", "name": "arange", "type": null}}, "concat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.CupyBackend.concat", "name": "concat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "arg_types": ["einops._backends.CupyBackend", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concat of CupyBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cupy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops._backends.CupyBackend.cupy", "name": "cupy", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "einsum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "pattern", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.CupyBackend.einsum", "name": "einsum", "type": null}}, "framework_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "einops._backends.CupyBackend.framework_name", "name": "framework_name", "type": "builtins.str"}}, "from_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.CupyBackend.from_numpy", "name": "from_numpy", "type": null}}, "is_appropriate_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.CupyBackend.is_appropriate_type", "name": "is_appropriate_type", "type": null}}, "is_float_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.CupyBackend.is_float_type", "name": "is_float_type", "type": null}}, "stack_on_zeroth_dimension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.CupyBackend.stack_on_zeroth_dimension", "name": "stack_on_zeroth_dimension", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "arg_types": ["einops._backends.CupyBackend", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_on_zeroth_dimension of CupyBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "repeats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.CupyBackend.tile", "name": "tile", "type": null}}, "to_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.CupyBackend.to_numpy", "name": "to_numpy", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops._backends.CupyBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops._backends.CupyBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HashableTuple": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops._backends.HashableTuple", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "einops._backends.HashableTuple", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops._backends", "mro": ["einops._backends.HashableTuple", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.HashableTuple.__getitem__", "name": "__getitem__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "elements"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.HashableTuple.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "elements"], "arg_types": ["einops._backends.HashableTuple", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HashableTuple", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.HashableTuple.__iter__", "name": "__iter__", "type": null}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.HashableTuple.__len__", "name": "__len__", "type": null}}, "elements": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops._backends.HashableTuple.elements", "name": "elements", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops._backends.HashableTuple.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops._backends.HashableTuple", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JaxBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["einops._backends.NumpyBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops._backends.JaxBackend", "name": "JaxBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "einops._backends.JaxBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops._backends", "mro": ["einops._backends.JaxBackend", "einops._backends.NumpyBackend", "einops._backends.AbstractBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.JaxBackend.__init__", "name": "__init__", "type": null}}, "framework_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "einops._backends.JaxBackend.framework_name", "name": "framework_name", "type": "builtins.str"}}, "from_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.JaxBackend.from_numpy", "name": "from_numpy", "type": null}}, "onp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops._backends.JaxBackend.onp", "name": "onp", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "to_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.JaxBackend.to_numpy", "name": "to_numpy", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops._backends.JaxBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops._backends.JaxBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NumpyBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["einops._backends.AbstractBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops._backends.NumpyBackend", "name": "NumpyBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "einops._backends.NumpyBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops._backends", "mro": ["einops._backends.NumpyBackend", "einops._backends.AbstractBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.NumpyBackend.__init__", "name": "__init__", "type": null}}, "add_axis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "new_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.NumpyBackend.add_axis", "name": "add_axis", "type": null}}, "arange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "start", "stop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.NumpyBackend.arange", "name": "arange", "type": null}}, "concat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.NumpyBackend.concat", "name": "concat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "arg_types": ["einops._backends.NumpyBackend", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concat of NumpyBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "einsum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "pattern", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.NumpyBackend.einsum", "name": "einsum", "type": null}}, "framework_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "einops._backends.NumpyBackend.framework_name", "name": "framework_name", "type": "builtins.str"}}, "from_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.NumpyBackend.from_numpy", "name": "from_numpy", "type": null}}, "is_appropriate_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.NumpyBackend.is_appropriate_type", "name": "is_appropriate_type", "type": null}}, "is_float_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.NumpyBackend.is_float_type", "name": "is_float_type", "type": null}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "implicit": true, "kind": "<PERSON><PERSON><PERSON>"}, "stack_on_zeroth_dimension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.NumpyBackend.stack_on_zeroth_dimension", "name": "stack_on_zeroth_dimension", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "arg_types": ["einops._backends.NumpyBackend", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_on_zeroth_dimension of NumpyBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "repeats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.NumpyBackend.tile", "name": "tile", "type": null}}, "to_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.NumpyBackend.to_numpy", "name": "to_numpy", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops._backends.NumpyBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops._backends.NumpyBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OneFlowBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["einops._backends.AbstractBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops._backends.OneFlowBackend", "name": "OneFlowBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "einops._backends.OneFlowBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops._backends", "mro": ["einops._backends.OneFlowBackend", "einops._backends.AbstractBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.OneFlowBackend.__init__", "name": "__init__", "type": null}}, "add_axes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "x", "n_axes", "pos2len"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.OneFlowBackend.add_axes", "name": "add_axes", "type": null}}, "add_axis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "new_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.OneFlowBackend.add_axis", "name": "add_axis", "type": null}}, "arange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "start", "stop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.OneFlowBackend.arange", "name": "arange", "type": null}}, "concat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.OneFlowBackend.concat", "name": "concat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "arg_types": ["einops._backends.OneFlowBackend", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concat of OneFlowBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "einsum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "pattern", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.OneFlowBackend.einsum", "name": "einsum", "type": null}}, "flow": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops._backends.OneFlowBackend.flow", "name": "flow", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "framework_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "einops._backends.OneFlowBackend.framework_name", "name": "framework_name", "type": "builtins.str"}}, "from_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.OneFlowBackend.from_numpy", "name": "from_numpy", "type": null}}, "is_appropriate_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.OneFlowBackend.is_appropriate_type", "name": "is_appropriate_type", "type": null}}, "is_float_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.OneFlowBackend.is_float_type", "name": "is_float_type", "type": null}}, "layers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.OneFlowBackend.layers", "name": "layers", "type": null}}, "reduce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "x", "operation", "reduced_axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.OneFlowBackend.reduce", "name": "reduce", "type": null}}, "stack_on_zeroth_dimension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.OneFlowBackend.stack_on_zeroth_dimension", "name": "stack_on_zeroth_dimension", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "arg_types": ["einops._backends.OneFlowBackend", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_on_zeroth_dimension of OneFlowBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "repeats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.OneFlowBackend.tile", "name": "tile", "type": null}}, "to_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.OneFlowBackend.to_numpy", "name": "to_numpy", "type": null}}, "transpose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.OneFlowBackend.transpose", "name": "transpose", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops._backends.OneFlowBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops._backends.OneFlowBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PaddleBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["einops._backends.AbstractBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops._backends.PaddleBackend", "name": "PaddleBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "einops._backends.PaddleBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops._backends", "mro": ["einops._backends.PaddleBackend", "einops._backends.AbstractBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PaddleBackend.__init__", "name": "__init__", "type": null}}, "add_axes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "x", "n_axes", "pos2len"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PaddleBackend.add_axes", "name": "add_axes", "type": null}}, "add_axis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "new_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PaddleBackend.add_axis", "name": "add_axis", "type": null}}, "arange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "start", "stop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PaddleBackend.arange", "name": "arange", "type": null}}, "concat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PaddleBackend.concat", "name": "concat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "arg_types": ["einops._backends.PaddleBackend", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concat of PaddleBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "einsum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "pattern", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PaddleBackend.einsum", "name": "einsum", "type": null}}, "framework_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "einops._backends.PaddleBackend.framework_name", "name": "framework_name", "type": "builtins.str"}}, "from_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PaddleBackend.from_numpy", "name": "from_numpy", "type": null}}, "is_appropriate_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PaddleBackend.is_appropriate_type", "name": "is_appropriate_type", "type": null}}, "is_float_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PaddleBackend.is_float_type", "name": "is_float_type", "type": null}}, "layers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PaddleBackend.layers", "name": "layers", "type": null}}, "paddle": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops._backends.PaddleBackend.paddle", "name": "paddle", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "reduce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "x", "operation", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PaddleBackend.reduce", "name": "reduce", "type": null}}, "reshape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PaddleBackend.reshape", "name": "reshape", "type": null}}, "shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PaddleBackend.shape", "name": "shape", "type": null}}, "stack_on_zeroth_dimension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PaddleBackend.stack_on_zeroth_dimension", "name": "stack_on_zeroth_dimension", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "arg_types": ["einops._backends.PaddleBackend", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_on_zeroth_dimension of PaddleBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "repeats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PaddleBackend.tile", "name": "tile", "type": null}}, "to_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PaddleBackend.to_numpy", "name": "to_numpy", "type": null}}, "transpose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PaddleBackend.transpose", "name": "transpose", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops._backends.PaddleBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops._backends.PaddleBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PyTensorBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["einops._backends.AbstractBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops._backends.PyTensorBackend", "name": "PyTensorBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "einops._backends.PyTensorBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops._backends", "mro": ["einops._backends.PyTensorBackend", "einops._backends.AbstractBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PyTensorBackend.__init__", "name": "__init__", "type": null}}, "add_axis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "new_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PyTensorBackend.add_axis", "name": "add_axis", "type": null}}, "arange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "start", "stop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PyTensorBackend.arange", "name": "arange", "type": null}}, "concat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PyTensorBackend.concat", "name": "concat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "arg_types": ["einops._backends.PyTensorBackend", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concat of PyTensorBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_symbol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PyTensorBackend.create_symbol", "name": "create_symbol", "type": null}}, "einsum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "pattern", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PyTensorBackend.einsum", "name": "einsum", "type": null}}, "eval_symbol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "symbol", "symbol_value_pairs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PyTensorBackend.eval_symbol", "name": "eval_symbol", "type": null}}, "framework_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "einops._backends.PyTensorBackend.framework_name", "name": "framework_name", "type": "builtins.str"}}, "from_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PyTensorBackend.from_numpy", "name": "from_numpy", "type": null}}, "is_appropriate_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PyTensorBackend.is_appropriate_type", "name": "is_appropriate_type", "type": null}}, "is_float_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PyTensorBackend.is_float_type", "name": "is_float_type", "type": null}}, "pt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops._backends.PyTensorBackend.pt", "name": "pt", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PyTensorBackend.shape", "name": "shape", "type": null}}, "stack_on_zeroth_dimension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PyTensorBackend.stack_on_zeroth_dimension", "name": "stack_on_zeroth_dimension", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "arg_types": ["einops._backends.PyTensorBackend", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_on_zeroth_dimension of PyTensorBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "repeats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PyTensorBackend.tile", "name": "tile", "type": null}}, "to_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.PyTensorBackend.to_numpy", "name": "to_numpy", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops._backends.PyTensorBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops._backends.PyTensorBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFKerasBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["einops._backends.AbstractBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops._backends.TFKerasBackend", "name": "TFKerasBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "einops._backends.TFKerasBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops._backends", "mro": ["einops._backends.TFKerasBackend", "einops._backends.AbstractBackend", "builtins.object"], "names": {".class": "SymbolTable", "K": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops._backends.TFKerasBackend.K", "name": "K", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TFKerasBackend.__init__", "name": "__init__", "type": null}}, "add_axis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "new_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TFKerasBackend.add_axis", "name": "add_axis", "type": null}}, "arange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "start", "stop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TFKerasBackend.arange", "name": "arange", "type": null}}, "concat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TFKerasBackend.concat", "name": "concat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "arg_types": ["einops._backends.TFKerasBackend", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concat of TFKerasBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_symbol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TFKerasBackend.create_symbol", "name": "create_symbol", "type": null}}, "eval_symbol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "symbol", "symbol_value_pairs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TFKerasBackend.eval_symbol", "name": "eval_symbol", "type": null}}, "framework_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "einops._backends.TFKerasBackend.framework_name", "name": "framework_name", "type": "builtins.str"}}, "is_appropriate_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TFKerasBackend.is_appropriate_type", "name": "is_appropriate_type", "type": null}}, "is_float_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TFKerasBackend.is_float_type", "name": "is_float_type", "type": null}}, "keras": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops._backends.TFKerasBackend.keras", "name": "keras", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TFKerasBackend.layers", "name": "layers", "type": null}}, "reduce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "x", "operation", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TFKerasBackend.reduce", "name": "reduce", "type": null}}, "reshape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TFKerasBackend.reshape", "name": "reshape", "type": null}}, "shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TFKerasBackend.shape", "name": "shape", "type": null}}, "stack_on_zeroth_dimension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TFKerasBackend.stack_on_zeroth_dimension", "name": "stack_on_zeroth_dimension", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "arg_types": ["einops._backends.TFKerasBackend", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_on_zeroth_dimension of TFKerasBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tf": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops._backends.TFKerasBackend.tf", "name": "tf", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "tile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "repeats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TFKerasBackend.tile", "name": "tile", "type": null}}, "transpose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TFKerasBackend.transpose", "name": "transpose", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops._backends.TFKerasBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops._backends.TFKerasBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TensorflowBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["einops._backends.AbstractBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops._backends.TensorflowBackend", "name": "TensorflowBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "einops._backends.TensorflowBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops._backends", "mro": ["einops._backends.TensorflowBackend", "einops._backends.AbstractBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TensorflowBackend.__init__", "name": "__init__", "type": null}}, "add_axis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "new_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TensorflowBackend.add_axis", "name": "add_axis", "type": null}}, "arange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "start", "stop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TensorflowBackend.arange", "name": "arange", "type": null}}, "concat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TensorflowBackend.concat", "name": "concat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "arg_types": ["einops._backends.TensorflowBackend", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concat of TensorflowBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "einsum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "pattern", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TensorflowBackend.einsum", "name": "einsum", "type": null}}, "framework_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "einops._backends.TensorflowBackend.framework_name", "name": "framework_name", "type": "builtins.str"}}, "from_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TensorflowBackend.from_numpy", "name": "from_numpy", "type": null}}, "is_appropriate_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TensorflowBackend.is_appropriate_type", "name": "is_appropriate_type", "type": null}}, "is_float_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TensorflowBackend.is_float_type", "name": "is_float_type", "type": null}}, "layers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TensorflowBackend.layers", "name": "layers", "type": null}}, "reduce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "x", "operation", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TensorflowBackend.reduce", "name": "reduce", "type": null}}, "reshape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TensorflowBackend.reshape", "name": "reshape", "type": null}}, "shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TensorflowBackend.shape", "name": "shape", "type": null}}, "stack_on_zeroth_dimension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TensorflowBackend.stack_on_zeroth_dimension", "name": "stack_on_zeroth_dimension", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "arg_types": ["einops._backends.TensorflowBackend", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_on_zeroth_dimension of TensorflowBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tf": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops._backends.TensorflowBackend.tf", "name": "tf", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "tile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "repeats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TensorflowBackend.tile", "name": "tile", "type": null}}, "to_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TensorflowBackend.to_numpy", "name": "to_numpy", "type": null}}, "transpose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TensorflowBackend.transpose", "name": "transpose", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops._backends.TensorflowBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops._backends.TensorflowBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TinygradBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["einops._backends.AbstractBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops._backends.TinygradBackend", "name": "TinygradBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "einops._backends.TinygradBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops._backends", "mro": ["einops._backends.TinygradBackend", "einops._backends.AbstractBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TinygradBackend.__init__", "name": "__init__", "type": null}}, "add_axis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "new_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TinygradBackend.add_axis", "name": "add_axis", "type": null}}, "arange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "start", "stop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TinygradBackend.arange", "name": "arange", "type": null}}, "concat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TinygradBackend.concat", "name": "concat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "arg_types": ["einops._backends.TinygradBackend", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concat of TinygradBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "einsum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "pattern", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TinygradBackend.einsum", "name": "einsum", "type": null}}, "framework_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "einops._backends.TinygradBackend.framework_name", "name": "framework_name", "type": "builtins.str"}}, "from_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TinygradBackend.from_numpy", "name": "from_numpy", "type": null}}, "is_appropriate_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TinygradBackend.is_appropriate_type", "name": "is_appropriate_type", "type": null}}, "is_float_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TinygradBackend.is_float_type", "name": "is_float_type", "type": null}}, "reduce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "x", "operation", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TinygradBackend.reduce", "name": "reduce", "type": null}}, "reshape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TinygradBackend.reshape", "name": "reshape", "type": null}}, "shape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TinygradBackend.shape", "name": "shape", "type": null}}, "stack_on_zeroth_dimension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TinygradBackend.stack_on_zeroth_dimension", "name": "stack_on_zeroth_dimension", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "arg_types": ["einops._backends.TinygradBackend", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_on_zeroth_dimension of TinygradBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "repeats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TinygradBackend.tile", "name": "tile", "type": null}}, "tinygrad": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops._backends.TinygradBackend.tinygrad", "name": "tinygrad", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "to_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TinygradBackend.to_numpy", "name": "to_numpy", "type": null}}, "transpose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TinygradBackend.transpose", "name": "transpose", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops._backends.TinygradBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops._backends.TinygradBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TorchBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["einops._backends.AbstractBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops._backends.TorchBackend", "name": "TorchBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "einops._backends.TorchBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops._backends", "mro": ["einops._backends.TorchBackend", "einops._backends.AbstractBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TorchBackend.__init__", "name": "__init__", "type": null}}, "add_axes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "x", "n_axes", "pos2len"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TorchBackend.add_axes", "name": "add_axes", "type": null}}, "add_axis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "new_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TorchBackend.add_axis", "name": "add_axis", "type": null}}, "arange": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "start", "stop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TorchBackend.arange", "name": "arange", "type": null}}, "concat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TorchBackend.concat", "name": "concat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensors", "axis"], "arg_types": ["einops._backends.TorchBackend", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "concat of TorchBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "einsum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "pattern", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TorchBackend.einsum", "name": "einsum", "type": null}}, "framework_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "einops._backends.TorchBackend.framework_name", "name": "framework_name", "type": "builtins.str"}}, "from_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TorchBackend.from_numpy", "name": "from_numpy", "type": null}}, "is_appropriate_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TorchBackend.is_appropriate_type", "name": "is_appropriate_type", "type": null}}, "is_float_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TorchBackend.is_float_type", "name": "is_float_type", "type": null}}, "layers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TorchBackend.layers", "name": "layers", "type": null}}, "reduce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "x", "operation", "reduced_axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TorchBackend.reduce", "name": "reduce", "type": null}}, "stack_on_zeroth_dimension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TorchBackend.stack_on_zeroth_dimension", "name": "stack_on_zeroth_dimension", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tensors"], "arg_types": ["einops._backends.TorchBackend", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stack_on_zeroth_dimension of TorchBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "repeats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TorchBackend.tile", "name": "tile", "type": null}}, "to_numpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TorchBackend.to_numpy", "name": "to_numpy", "type": null}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "implicit": true, "kind": "<PERSON><PERSON><PERSON>"}, "transpose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.TorchBackend.transpose", "name": "transpose", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops._backends.TorchBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops._backends.TorchBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnknownSize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops._backends.UnknownSize", "name": "UnknownSize", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "einops._backends.UnknownSize", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops._backends", "mro": ["einops._backends.UnknownSize", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.UnknownSize.__eq__", "name": "__eq__", "type": null}}, "__floordiv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.UnknownSize.__floordiv__", "name": "__floordiv__", "type": null}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.UnknownSize.__hash__", "name": "__hash__", "type": null}}, "__mul__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.UnknownSize.__mul__", "name": "__mul__", "type": null}}, "__rmul__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.UnknownSize.__rmul__", "name": "__rmul__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops._backends.UnknownSize.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops._backends.UnknownSize", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops._backends.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__author__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "einops._backends.__author__", "name": "__author__", "type": "builtins.str"}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops._backends.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops._backends.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops._backends.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops._backends.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops._backends.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_debug_importing": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "einops._backends._debug_importing", "name": "_debug_importing", "type": "builtins.bool"}}, "_loaded_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "einops._backends._loaded_backends", "name": "_loaded_backends", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_type2backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "einops._backends._type2backend", "name": "_type2backend", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "get_backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops._backends.get_backend", "name": "get_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tensor"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_backend", "ret_type": "einops._backends.AbstractBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\einops\\_backends.py"}