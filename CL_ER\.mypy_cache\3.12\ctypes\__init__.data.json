{".class": "MypyFile", "_fullname": "ctypes", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ARRAY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["typ", "len"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.ARRAY", "name": "ARRAY", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["typ", "len"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._CT", "id": -1, "name": "_CT", "namespace": "ctypes.ARRAY", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ARRAY", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._CT", "id": -1, "name": "_CT", "namespace": "ctypes.ARRAY", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._CT", "id": -1, "name": "_CT", "namespace": "ctypes.ARRAY", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}]}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ArgumentError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.ArgumentError", "name": "ArgumentError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.ArgumentError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.ArgumentError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.ArgumentError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.ArgumentError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Array": {".class": "SymbolTableNode", "cross_ref": "_ctypes.Array", "kind": "Gdef"}, "BigEndianStructure": {".class": "SymbolTableNode", "cross_ref": "ctypes._endian.BigEndianStructure", "kind": "Gdef"}, "BigEndianUnion": {".class": "SymbolTableNode", "cross_ref": "ctypes._endian.BigEndianUnion", "kind": "Gdef"}, "CDLL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.CDLL", "name": "CDLL", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.CDLL", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.CDLL", "builtins.object"], "names": {".class": "SymbolTable", "_FuncPtr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.CDLL._FuncPtr", "name": "_FuncPtr", "type": {".class": "TypeType", "item": "ctypes._CDLLFuncPointer"}}}, "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.CDLL.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["ctypes.CDLL", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of CDLL", "ret_type": "ctypes._NamedFuncPointer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.CDLL.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["ctypes.CDLL", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of CDLL", "ret_type": "ctypes._NamedFuncPointer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "mode", "handle", "use_errno", "use_last_error", "winmode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.CDLL.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "mode", "handle", "use_errno", "use_last_error", "winmode"], "arg_types": ["ctypes.CDLL", {".class": "TypeAliasType", "args": [], "type_ref": "ctypes._NameTypes"}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CDLL", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_func_flags_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "ctypes.CDLL._func_flags_", "name": "_func_flags_", "type": "builtins.int"}}, "_func_restype_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "ctypes.CDLL._func_restype_", "name": "_func_restype_", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}}, {".class": "TypeType", "item": "_ctypes.CFuncPtr"}, {".class": "TypeType", "item": "_ctypes.Union"}, {".class": "TypeType", "item": "_ctypes.Structure"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes.Array"}}], "uses_pep604_syntax": false}}}, "_handle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.CDLL._handle", "name": "_handle", "type": "builtins.int"}}, "_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes.CDLL._name", "name": "_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.CDLL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.CDLL", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CFUNCTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5], "arg_names": ["restype", "argtypes", "use_errno", "use_last_error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.CFUNCTYPE", "name": "CFUNCTYPE", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5], "arg_names": ["restype", "argtypes", "use_errno", "use_last_error"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "_ctypes._CData"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}}, {".class": "TypeType", "item": "_ctypes.CFuncPtr"}, {".class": "TypeType", "item": "_ctypes.Union"}, {".class": "TypeType", "item": "_ctypes.Structure"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes.Array"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "_ctypes._CData"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}}, {".class": "TypeType", "item": "_ctypes.CFuncPtr"}, {".class": "TypeType", "item": "_ctypes.Union"}, {".class": "TypeType", "item": "_ctypes.Structure"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes.Array"}}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "CFUNCTYPE", "ret_type": {".class": "TypeType", "item": "ctypes._CFunctionType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DEFAULT_MODE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.DEFAULT_MODE", "name": "DEFAULT_MODE", "type": "builtins.int"}}, "DllCanUnloadNow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.DllCanUnloadNow", "name": "DllCanUnloadNow", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "DllCanUnloadNow", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DllGetClassObject": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["rclsid", "riid", "ppv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.DllGetClassObject", "name": "DllGetClassObject", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["rclsid", "riid", "ppv"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "DllGetClassObject", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "FormatError": {".class": "SymbolTableNode", "cross_ref": "_ctypes.FormatError", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GenericAlias": {".class": "SymbolTableNode", "cross_ref": "types.GenericAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GetLastError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.GetLastError", "name": "GetLastError", "type": "ctypes._GetLastErrorFunctionType"}}, "HRESULT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.HRESULT", "name": "HRESULT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.HRESULT", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.HRESULT", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.HRESULT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.HRESULT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LibraryLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.LibraryLoader", "name": "LibraryLoader", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._DLLT", "id": 1, "name": "_DLLT", "namespace": "ctypes.LibraryLoader", "upper_bound": "ctypes.CDLL", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.LibraryLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.LibraryLoader", "builtins.object"], "names": {".class": "SymbolTable", "LoadLibrary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.LibraryLoader.LoadLibrary", "name": "LoadLibrary", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._DLLT", "id": 1, "name": "_DLLT", "namespace": "ctypes.LibraryLoader", "upper_bound": "ctypes.CDLL", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "ctypes.LibraryLoader"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "LoadLibrary of LibraryLoader", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._DLLT", "id": 1, "name": "_DLLT", "namespace": "ctypes.LibraryLoader", "upper_bound": "ctypes.CDLL", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__class_getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class"], "fullname": "ctypes.LibraryLoader.__class_getitem__", "name": "__class_getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._DLLT", "id": 1, "name": "_DLLT", "namespace": "ctypes.LibraryLoader", "upper_bound": "ctypes.CDLL", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "ctypes.LibraryLoader"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__class_getitem__ of LibraryLoader", "ret_type": "types.GenericAlias", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.LibraryLoader.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._DLLT", "id": 1, "name": "_DLLT", "namespace": "ctypes.LibraryLoader", "upper_bound": "ctypes.CDLL", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "ctypes.LibraryLoader"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of LibraryLoader", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._DLLT", "id": 1, "name": "_DLLT", "namespace": "ctypes.LibraryLoader", "upper_bound": "ctypes.CDLL", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.LibraryLoader.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._DLLT", "id": 1, "name": "_DLLT", "namespace": "ctypes.LibraryLoader", "upper_bound": "ctypes.CDLL", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "ctypes.LibraryLoader"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of LibraryLoader", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._DLLT", "id": 1, "name": "_DLLT", "namespace": "ctypes.LibraryLoader", "upper_bound": "ctypes.CDLL", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dlltype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.LibraryLoader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dlltype"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._DLLT", "id": 1, "name": "_DLLT", "namespace": "ctypes.LibraryLoader", "upper_bound": "ctypes.CDLL", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "ctypes.LibraryLoader"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._DLLT", "id": 1, "name": "_DLLT", "namespace": "ctypes.LibraryLoader", "upper_bound": "ctypes.CDLL", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LibraryLoader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.LibraryLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._DLLT", "id": 1, "name": "_DLLT", "namespace": "ctypes.LibraryLoader", "upper_bound": "ctypes.CDLL", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "ctypes.LibraryLoader"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_DLLT"], "typeddict_type": null}}, "LittleEndianStructure": {".class": "SymbolTableNode", "cross_ref": "ctypes._endian.LittleEndianStructure", "kind": "Gdef"}, "LittleEndianUnion": {".class": "SymbolTableNode", "cross_ref": "ctypes._endian.LittleEndianUnion", "kind": "Gdef"}, "OleDLL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ctypes.CDLL"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.OleDLL", "name": "OleDLL", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.OleDLL", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.OleDLL", "ctypes.CDLL", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.OleDLL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.OleDLL", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "POINTER": {".class": "SymbolTableNode", "cross_ref": "_ctypes.POINTER", "kind": "Gdef"}, "PYFUNCTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["restype", "argtypes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.PYFUNCTYPE", "name": "PYFUNCTYPE", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["restype", "argtypes"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "_ctypes._CData"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}}, {".class": "TypeType", "item": "_ctypes.CFuncPtr"}, {".class": "TypeType", "item": "_ctypes.Union"}, {".class": "TypeType", "item": "_ctypes.Structure"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes.Array"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "_ctypes._CData"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}}, {".class": "TypeType", "item": "_ctypes.CFuncPtr"}, {".class": "TypeType", "item": "_ctypes.Union"}, {".class": "TypeType", "item": "_ctypes.Structure"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes.Array"}}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "PYFUNCTYPE", "ret_type": {".class": "TypeType", "item": "ctypes._CFunctionType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "PyDLL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ctypes.CDLL"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.PyDLL", "name": "PyDLL", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.PyDLL", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.PyDLL", "ctypes.CDLL", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.PyDLL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.PyDLL", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RTLD_GLOBAL": {".class": "SymbolTableNode", "cross_ref": "_ctypes.RTLD_GLOBAL", "kind": "Gdef"}, "RTLD_LOCAL": {".class": "SymbolTableNode", "cross_ref": "_ctypes.RTLD_LOCAL", "kind": "Gdef"}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SetPointerType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["pointer", "cls"], "dataclass_transform_spec": null, "deprecated": "function ctypes.SetPointerType is deprecated: Deprecated in Python 3.13; removal scheduled for Python 3.15", "flags": ["is_decorated"], "fullname": "ctypes.SetPointerType", "name": "SetPointerType", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["pointer", "cls"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SetPointerType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ctypes.SetPointerType", "name": "SetPointerType", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["pointer", "cls"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SetPointerType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "StrPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.StrPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Structure": {".class": "SymbolTableNode", "cross_ref": "_ctypes.Structure", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "_ctypes.Union", "kind": "Gdef"}, "WINFUNCTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5], "arg_names": ["restype", "argtypes", "use_errno", "use_last_error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.WINFUNCTYPE", "name": "WINFUNCTYPE", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5], "arg_names": ["restype", "argtypes", "use_errno", "use_last_error"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "_ctypes._CData"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}}, {".class": "TypeType", "item": "_ctypes.CFuncPtr"}, {".class": "TypeType", "item": "_ctypes.Union"}, {".class": "TypeType", "item": "_ctypes.Structure"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes.Array"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "_ctypes._CData"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}}, {".class": "TypeType", "item": "_ctypes.CFuncPtr"}, {".class": "TypeType", "item": "_ctypes.Union"}, {".class": "TypeType", "item": "_ctypes.Structure"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes.Array"}}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WINFUNCTYPE", "ret_type": {".class": "TypeType", "item": "ctypes._CFunctionType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "WinDLL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ctypes.CDLL"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.WinDLL", "name": "WinDLL", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.WinDLL", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.WinDLL", "ctypes.CDLL", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.WinDLL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.WinDLL", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WinError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["code", "descr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.WinE<PERSON>r", "name": "Win<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["code", "descr"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Win<PERSON><PERSON><PERSON>", "ret_type": "builtins.OSError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_CArgObject": {".class": "SymbolTableNode", "cross_ref": "_ctypes._CArgObject", "kind": "Gdef"}, "_CDLLFuncPointer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ctypes.CFuncPtr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes._CDLLFuncPointer", "name": "_CDLL<PERSON>un<PERSON>Pointer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes._CDLLFuncPointer", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCFuncPtrType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes._CDLLFuncPointer", "_ctypes.CFuncPtr", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "_flags_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "ctypes._CDLLFuncPointer._flags_", "name": "_flags_", "type": "builtins.int"}}, "_restype_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "ctypes._CDLLFuncPointer._restype_", "name": "_restype_", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}}, {".class": "TypeType", "item": "_ctypes.CFuncPtr"}, {".class": "TypeType", "item": "_ctypes.Union"}, {".class": "TypeType", "item": "_ctypes.Structure"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes.Array"}}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._CDLLFuncPointer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes._CDLLFuncPointer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CData": {".class": "SymbolTableNode", "cross_ref": "_ctypes._CData", "kind": "Gdef"}, "_CDataType": {".class": "SymbolTableNode", "cross_ref": "_ctypes._CDataType", "kind": "Gdef"}, "_CField": {".class": "SymbolTableNode", "cross_ref": "_ctypes._CField", "kind": "Gdef"}, "_CFuncPtr": {".class": "SymbolTableNode", "cross_ref": "_ctypes.CFuncPtr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CFunctionType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ctypes.CFuncPtr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes._CFunctionType", "name": "_CFunctionType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes._CFunctionType", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCFuncPtrType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes._CFunctionType", "_ctypes.CFuncPtr", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "_argtypes_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "ctypes._CFunctionType._argtypes_", "name": "_argtypes_", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "_ctypes._CData"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}}, {".class": "TypeType", "item": "_ctypes.CFuncPtr"}, {".class": "TypeType", "item": "_ctypes.Union"}, {".class": "TypeType", "item": "_ctypes.Structure"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes.Array"}}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_flags_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "ctypes._CFunctionType._flags_", "name": "_flags_", "type": "builtins.int"}}, "_restype_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "ctypes._CFunctionType._restype_", "name": "_restype_", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "_ctypes._CData"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}}, {".class": "TypeType", "item": "_ctypes.CFuncPtr"}, {".class": "TypeType", "item": "_ctypes.Union"}, {".class": "TypeType", "item": "_ctypes.Structure"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes.Array"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._CFunctionType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes._CFunctionType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._CT", "name": "_CT", "upper_bound": "_ctypes._CData", "values": [], "variance": 0}}, "_CVoidConstPLike": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes._CVoidConstPLike", "line": 143, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "ctypes._CVoidPLike"}, "builtins.bytes"], "uses_pep604_syntax": true}}}, "_CVoidPLike": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes._CVoidPLike", "line": 138, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["_ctypes._PointerLike", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "_ctypes._CArgObject", "builtins.int"], "uses_pep604_syntax": true}}}, "_CanCastTo": {".class": "SymbolTableNode", "cross_ref": "_ctypes._CanCastTo", "kind": "Gdef"}, "_CastT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._CastT", "name": "_CastT", "upper_bound": "_ctypes._CanCastTo", "values": [], "variance": 0}}, "_DLLT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._DLLT", "name": "_DLLT", "upper_bound": "ctypes.CDLL", "values": [], "variance": 0}}, "_FuncPointer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes._FuncPointer", "line": 117, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["ctypes._CDLLFuncPointer", "ctypes._CFunctionType"], "uses_pep604_syntax": true}}}, "_GetLastErrorFunctionType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ctypes._NamedFuncPointer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes._GetLastErrorFunctionType", "name": "_GetLastErrorFunctionType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes._GetLastErrorFunctionType", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCFuncPtrType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes._GetLastErrorFunctionType", "ctypes._NamedFuncPointer", "ctypes._CDLLFuncPointer", "_ctypes.CFuncPtr", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes._GetLastErrorFunctionType.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["ctypes._GetLastErrorFunctionType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _GetLastErrorFunctionType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._GetLastErrorFunctionType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes._GetLastErrorFunctionType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MemmoveFunctionType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ctypes._CFunctionType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes._MemmoveFunctionType", "name": "_MemmoveFunctionType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes._MemmoveFunctionType", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCFuncPtrType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes._MemmoveFunctionType", "ctypes._CFunctionType", "_ctypes.CFuncPtr", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dst", "src", "count"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes._MemmoveFunctionType.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dst", "src", "count"], "arg_types": ["ctypes._MemmoveFunctionType", {".class": "TypeAliasType", "args": [], "type_ref": "ctypes._CVoidPLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "ctypes._CVoidConstPLike"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _MemmoveFunctionType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._MemmoveFunctionType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes._MemmoveFunctionType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MemsetFunctionType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ctypes._CFunctionType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes._MemsetFunctionType", "name": "_MemsetFunctionType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes._MemsetFunctionType", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCFuncPtrType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes._MemsetFunctionType", "ctypes._CFunctionType", "_ctypes.CFuncPtr", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dst", "c", "count"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes._MemsetFunctionType.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dst", "c", "count"], "arg_types": ["ctypes._MemsetFunctionType", {".class": "TypeAliasType", "args": [], "type_ref": "ctypes._CVoidPLike"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _MemsetFunctionType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._MemsetFunctionType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes._MemsetFunctionType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_NameTypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "ctypes._NameTypes", "line": 62, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_NamedFuncPointer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ctypes._CDLLFuncPointer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes._NamedFuncPointer", "name": "_Named<PERSON><PERSON><PERSON>Pointer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes._NamedFuncPointer", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCFuncPtrType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes._NamedFuncPointer", "ctypes._CDLLFuncPointer", "_ctypes.CFuncPtr", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "ctypes._NamedFuncPointer.__name__", "name": "__name__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._NamedFuncPointer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes._NamedFuncPointer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Pointer": {".class": "SymbolTableNode", "cross_ref": "_ctypes._Pointer", "kind": "Gdef"}, "_PointerLike": {".class": "SymbolTableNode", "cross_ref": "_ctypes._PointerLike", "kind": "Gdef"}, "_SimpleCData": {".class": "SymbolTableNode", "cross_ref": "_ctypes._SimpleCData", "kind": "Gdef"}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "addressof": {".class": "SymbolTableNode", "cross_ref": "_ctypes.addressof", "kind": "Gdef"}, "alignment": {".class": "SymbolTableNode", "cross_ref": "_ctypes.alignment", "kind": "Gdef"}, "byref": {".class": "SymbolTableNode", "cross_ref": "_ctypes.byref", "kind": "Gdef"}, "c_bool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_bool", "name": "c_bool", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_bool", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_bool", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.c_bool.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "value"], "arg_types": ["ctypes.c_bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of c_bool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_bool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_bool", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_buffer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "ctypes.c_buffer", "name": "c_buffer", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["init", "size"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", "builtins.bytes"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["ctypes.c_char"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "c_byte": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_byte", "name": "c_byte", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_byte", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_byte", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_byte.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_byte", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_char": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_char", "name": "c_char", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_char", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_char", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.c_char.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "value"], "arg_types": ["ctypes.c_char", {".class": "UnionType", "items": ["builtins.int", "builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of c_char", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_char.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_char", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_char_p": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ctypes._PointerLike", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_char_p", "name": "c_char_p", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_char_p", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_char_p", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.c_char_p.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "value"], "arg_types": ["ctypes.c_char_p", {".class": "UnionType", "items": ["builtins.int", "builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of c_char_p", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "ctypes.c_char_p.from_param", "name": "from_param", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_char_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_char_p", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_param of c_char_p", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_char_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_char_p", "values": [], "variance": 0}, "_ctypes._CArgObject"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_char_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_char_p", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "ctypes.c_char_p.from_param", "name": "from_param", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_char_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_char_p", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_param of c_char_p", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_char_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_char_p", "values": [], "variance": 0}, "_ctypes._CArgObject"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_char_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_char_p", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_char_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_char_p", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_double": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_double", "name": "c_double", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_double", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_double", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_double.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_double", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_float": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_float", "name": "c_float", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_float", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_float", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_float.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_float", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_int": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_int", "name": "c_int", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_int", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_int", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_int.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_int", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_int16": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_int16", "name": "c_int16", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_int16", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_int16", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_int16.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_int16", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_int32": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_int32", "name": "c_int32", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_int32", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_int32", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_int32.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_int32", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_int64": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_int64", "name": "c_int64", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_int64", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_int64", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_int64.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_int64", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_int8": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.c_int8", "line": 227, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_byte"}}, "c_long": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_long", "name": "c_long", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_long", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_long", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_long.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_long", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_longdouble": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_longdouble", "name": "c_longdouble", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_longdouble", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_longdouble", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_longdouble.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_longdouble", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_longlong": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_longlong", "name": "c_longlong", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_longlong", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_longlong", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_longlong.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_longlong", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_short": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_short", "name": "c_short", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_short", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_short", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_short.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_short", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_size_t": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_size_t", "name": "c_size_t", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_size_t", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_size_t", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_size_t.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_size_t", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_ssize_t": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_ssize_t", "name": "c_ssize_t", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_ssize_t", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_ssize_t", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_ssize_t.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_ssize_t", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_time_t": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.c_time_t", "name": "c_time_t", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "ctypes.c_int32"}, {".class": "TypeType", "item": "ctypes.c_int64"}, {".class": "TypeType", "item": "ctypes.c_short"}, {".class": "TypeType", "item": "ctypes.c_int"}, {".class": "TypeType", "item": "ctypes.c_long"}, {".class": "TypeType", "item": "ctypes.c_longlong"}], "uses_pep604_syntax": false}}}, "c_ubyte": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_ubyte", "name": "c_ubyte", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_ubyte", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_ubyte", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_ubyte.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_ubyte", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_uint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_uint", "name": "c_uint", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_uint", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_uint", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_uint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_uint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_uint16": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_uint16", "name": "c_uint16", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_uint16", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_uint16", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_uint16.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_uint16", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_uint32": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_uint32", "name": "c_uint32", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_uint32", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_uint32", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_uint32.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_uint32", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_uint64": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_uint64", "name": "c_uint64", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_uint64", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_uint64", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_uint64.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_uint64", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_uint8": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.c_uint8", "line": 234, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_ubyte"}}, "c_ulong": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_ulong", "name": "c_ulong", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_ulong", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_ulong", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_ulong.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_ulong", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_ulonglong": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_ulonglong", "name": "c_ul<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_ulonglong", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_ulonglong", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_ulonglong.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_ulonglong", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_ushort": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_ushort", "name": "c_ushort", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_ushort", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_ushort", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_ushort.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_ushort", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_void_p": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ctypes._PointerLike", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_void_p", "name": "c_void_p", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_void_p", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_void_p", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "from_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "ctypes.c_void_p.from_param", "name": "from_param", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_void_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_void_p", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_param of c_void_p", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_void_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_void_p", "values": [], "variance": 0}, "_ctypes._CArgObject"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_void_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_void_p", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "ctypes.c_void_p.from_param", "name": "from_param", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_void_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_void_p", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_param of c_void_p", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_void_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_void_p", "values": [], "variance": 0}, "_ctypes._CArgObject"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_void_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_void_p", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_void_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_void_p", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_voidp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "ctypes.c_voidp", "line": 223, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ctypes.c_void_p"}}, "c_wchar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_wchar", "name": "c_wchar", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_wchar", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_wchar", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_wchar.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_wchar", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "c_wchar_p": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ctypes._PointerLike", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.c_wchar_p", "name": "c_wchar_p", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.c_wchar_p", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.c_wchar_p", "_ctypes._PointerLike", "_ctypes._CanCastTo", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.c_wchar_p.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "value"], "arg_types": ["ctypes.c_wchar_p", {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of c_wchar_p", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "ctypes.c_wchar_p.from_param", "name": "from_param", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_wchar_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_wchar_p", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_param of c_wchar_p", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_wchar_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_wchar_p", "values": [], "variance": 0}, "_ctypes._CArgObject"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_wchar_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_wchar_p", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "ctypes.c_wchar_p.from_param", "name": "from_param", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_wchar_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_wchar_p", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_param of c_wchar_p", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_wchar_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_wchar_p", "values": [], "variance": 0}, "_ctypes._CArgObject"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_wchar_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_wchar_p", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.c_wchar_p.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes.c_wchar_p", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "cast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["obj", "typ"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.cast", "name": "cast", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["obj", "typ"], "arg_types": [{".class": "UnionType", "items": ["_ctypes._CData", {".class": "TypeAliasType", "args": [], "type_ref": "_ctypes._CDataType"}, "_ctypes._CArgObject", "builtins.int"], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._CastT", "id": -1, "name": "_CastT", "namespace": "ctypes.cast", "upper_bound": "_ctypes._CanCastTo", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cast", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._CastT", "id": -1, "name": "_CastT", "namespace": "ctypes.cast", "upper_bound": "_ctypes._CanCastTo", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._CastT", "id": -1, "name": "_CastT", "namespace": "ctypes.cast", "upper_bound": "_ctypes._CanCastTo", "values": [], "variance": 0}]}}}, "cdll": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.cdll", "name": "cdll", "type": {".class": "Instance", "args": ["ctypes.CDLL"], "extra_attrs": null, "type_ref": "ctypes.LibraryLoader"}}}, "create_string_buffer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["init", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.create_string_buffer", "name": "create_string_buffer", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["init", "size"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_string_buffer", "ret_type": {".class": "Instance", "args": ["ctypes.c_char"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_unicode_buffer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["init", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.create_unicode_buffer", "name": "create_unicode_buffer", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["init", "size"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_unicode_buffer", "ret_type": {".class": "Instance", "args": ["ctypes.c_wchar"], "extra_attrs": null, "type_ref": "_ctypes.Array"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_errno": {".class": "SymbolTableNode", "cross_ref": "_ctypes.get_errno", "kind": "Gdef"}, "get_last_error": {".class": "SymbolTableNode", "cross_ref": "_ctypes.get_last_error", "kind": "Gdef"}, "memmove": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.memmove", "name": "memmove", "type": "ctypes._MemmoveFunctionType"}}, "memset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.memset", "name": "memset", "type": "ctypes._MemsetFunctionType"}}, "oledll": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.oledll", "name": "oledll", "type": {".class": "Instance", "args": ["ctypes.OleDLL"], "extra_attrs": null, "type_ref": "ctypes.LibraryLoader"}}}, "pointer": {".class": "SymbolTableNode", "cross_ref": "_ctypes.pointer", "kind": "Gdef"}, "py_object": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ctypes._CanCastTo", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._T", "id": 1, "name": "_T", "namespace": "ctypes.py_object", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes.py_object", "name": "py_object", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._T", "id": 1, "name": "_T", "namespace": "ctypes.py_object", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes.py_object", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCSimpleType", "metadata": {}, "module_name": "ctypes", "mro": ["ctypes.py_object", "_ctypes._CanCastTo", "_ctypes._SimpleCData", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes.py_object.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._T", "id": 1, "name": "_T", "namespace": "ctypes.py_object", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "ctypes.py_object"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_T"], "typeddict_type": null}}, "pydll": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.pydll", "name": "pydll", "type": {".class": "Instance", "args": ["ctypes.PyDLL"], "extra_attrs": null, "type_ref": "ctypes.LibraryLoader"}}}, "pythonapi": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.python<PERSON>i", "name": "python<PERSON><PERSON>", "type": "ctypes.PyDLL"}}, "resize": {".class": "SymbolTableNode", "cross_ref": "_ctypes.resize", "kind": "Gdef"}, "set_errno": {".class": "SymbolTableNode", "cross_ref": "_ctypes.set_errno", "kind": "Gdef"}, "set_last_error": {".class": "SymbolTableNode", "cross_ref": "_ctypes.set_last_error", "kind": "Gdef"}, "sizeof": {".class": "SymbolTableNode", "cross_ref": "_ctypes.sizeof", "kind": "Gdef"}, "string_at": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["ptr", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.string_at", "name": "string_at", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["ptr", "size"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "ctypes._CVoidConstPLike"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "string_at", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "type_check_only": {".class": "SymbolTableNode", "cross_ref": "typing.type_check_only", "kind": "Gdef", "module_hidden": true, "module_public": false}, "windll": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes.windll", "name": "windll", "type": {".class": "Instance", "args": ["ctypes.WinDLL"], "extra_attrs": null, "type_ref": "ctypes.LibraryLoader"}}}, "wstring_at": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["ptr", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ctypes.wstring_at", "name": "wstring_at", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["ptr", "size"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "ctypes._CVoidConstPLike"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wstring_at", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\ctypes\\__init__.pyi"}