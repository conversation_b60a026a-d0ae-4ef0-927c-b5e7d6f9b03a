{".class": "MypyFile", "_fullname": "kornia.augmentation.auto.autoaugment.autoaugment", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AutoAugment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.auto.base.PolicyAugmentBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.autoaugment.autoaugment.AutoAugment", "name": "AutoAugment", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.autoaugment.autoaugment.AutoAugment", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.autoaugment.autoaugment", "mro": ["kornia.augmentation.auto.autoaugment.autoaugment.AutoAugment", "kornia.augmentation.auto.base.PolicyAugmentBase", "kornia.augmentation.container.base.ImageSequentialBase", "kornia.augmentation.container.base.SequentialBase", "kornia.augmentation.container.base.BasicSequentialBase", "torch.nn.modules.container.Sequential", "torch.nn.modules.module.Module", "kornia.augmentation.container.base.TransformMatrixMinIn", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "policy", "transformation_matrix_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.autoaugment.autoaugment.AutoAugment.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "policy", "transformation_matrix_mode"], "arg_types": ["kornia.augmentation.auto.autoaugment.autoaugment.AutoAugment", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "kornia.augmentation.auto.base.SUBPOLICY_CONFIG"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AutoAugment", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compose_subpolicy_sequential": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "subpolicy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.autoaugment.autoaugment.AutoAugment.compose_subpolicy_sequential", "name": "compose_subpolicy_sequential", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "subpolicy"], "arg_types": ["kornia.augmentation.auto.autoaugment.autoaugment.AutoAugment", {".class": "TypeAliasType", "args": [], "type_ref": "kornia.augmentation.auto.base.SUBPOLICY_CONFIG"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compose_subpolicy_sequential of AutoAugment", "ret_type": "kornia.augmentation.auto.operations.policy.PolicySequential", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_forward_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.autoaugment.autoaugment.AutoAugment.get_forward_sequence", "name": "get_forward_sequence", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "params"], "arg_types": ["kornia.augmentation.auto.autoaugment.autoaugment.AutoAugment", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "kornia.augmentation.container.params.ParamItem"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_forward_sequence of AutoAugment", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "torch.nn.modules.module.Module"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rand_selector": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "kornia.augmentation.auto.autoaugment.autoaugment.AutoAugment.rand_selector", "name": "rand_selector", "type": "torch.distributions.categorical.Categorical"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.autoaugment.autoaugment.AutoAugment.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.autoaugment.autoaugment.AutoAugment", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Categorical": {".class": "SymbolTableNode", "cross_ref": "torch.distributions.categorical.Categorical", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Module": {".class": "SymbolTableNode", "cross_ref": "kornia.core._backend.Module", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ParamItem": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.container.params.ParamItem", "kind": "Gdef"}, "PolicyAugmentBase": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.base.PolicyAugmentBase", "kind": "Gdef"}, "PolicySequential": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.policy.PolicySequential", "kind": "Gdef"}, "SUBPOLICY_CONFIG": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.base.SUBPOLICY_CONFIG", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.autoaugment.autoaugment.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.autoaugment.autoaugment.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.autoaugment.autoaugment.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.autoaugment.autoaugment.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.autoaugment.autoaugment.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.autoaugment.autoaugment.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cifar10_policy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "kornia.augmentation.auto.autoaugment.autoaugment.cifar10_policy", "name": "cifar10_policy", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "kornia.augmentation.auto.base.SUBPOLICY_CONFIG"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "imagenet_policy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "kornia.augmentation.auto.autoaugment.autoaugment.imagenet_policy", "name": "imagenet_policy", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "kornia.augmentation.auto.base.SUBPOLICY_CONFIG"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ops": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.autoaugment.ops", "kind": "Gdef"}, "svhn_policy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "kornia.augmentation.auto.autoaugment.autoaugment.svhn_policy", "name": "svhn_policy", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "kornia.augmentation.auto.base.SUBPOLICY_CONFIG"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "tensor": {".class": "SymbolTableNode", "cross_ref": "kornia.core._backend.tensor", "kind": "Gdef"}}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\kornia\\augmentation\\auto\\autoaugment\\autoaugment.py"}