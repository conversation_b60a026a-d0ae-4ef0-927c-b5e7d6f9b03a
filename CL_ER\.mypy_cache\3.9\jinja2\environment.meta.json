{"data_mtime": 1754286351, "dep_lines": [17, 18, 20, 36, 41, 45, 46, 49, 60, 61, 62, 937, 5, 6, 8, 9, 10, 13, 15, 17, 58, 861, 1283, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 25, 25, 20, 20, 10, 10, 10, 5, 5, 5, 5, 20, 25, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["jinja2.nodes", "jinja2.compiler", "jinja2.defaults", "jinja2.exceptions", "jinja2.lexer", "jinja2.parser", "jinja2.runtime", "jinja2.utils", "jinja2.bccache", "jinja2.ext", "jinja2.loaders", "jinja2.debug", "os", "typing", "weakref", "collections", "functools", "types", "markupsafe", "jinja2", "typing_extensions", "zipfile", "asyncio", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "jinja2.filters", "jinja2.tests", "jinja2.visitor"], "hash": "784930e44403401691e9aba840b2d38aed74230f", "id": "jinja2.environment", "ignore_all": true, "interface_hash": "60b43769defb10e90a34d052e5f4716c449c3182", "mtime": 1716993775, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\jinja2\\environment.py", "plugin_data": null, "size": 61538, "suppressed": [], "version_id": "1.15.0"}