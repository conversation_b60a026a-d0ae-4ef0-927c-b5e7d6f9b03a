{".class": "MypyFile", "_fullname": "ctypes._endian", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BigEndianStructure": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ctypes.Structure"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes._endian.BigEndianStructure", "name": "BigEndianStructure", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes._endian.BigEndianStructure", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCStructType", "metadata": {}, "module_name": "ctypes._endian", "mro": ["ctypes._endian.BigEndianStructure", "_ctypes.Structure", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._endian.BigEndianStructure.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes._endian.BigEndianStructure", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LittleEndianStructure": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ctypes.Structure"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ctypes._endian.LittleEndianStructure", "name": "LittleEndianStructure", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "ctypes._endian.LittleEndianStructure", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCStructType", "metadata": {}, "module_name": "ctypes._endian", "mro": ["ctypes._endian.LittleEndianStructure", "_ctypes.Structure", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ctypes._endian.LittleEndianStructure.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ctypes._endian.LittleEndianStructure", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Structure": {".class": "SymbolTableNode", "cross_ref": "_ctypes.Structure", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "_ctypes.Union", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes._endian.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes._endian.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes._endian.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes._endian.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes._endian.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ctypes._endian.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\ctypes\\_endian.pyi"}