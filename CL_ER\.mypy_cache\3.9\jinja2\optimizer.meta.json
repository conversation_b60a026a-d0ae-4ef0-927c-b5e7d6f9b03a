{"data_mtime": 1754286351, "dep_lines": [13, 14, 17, 11, 13, 1, 1, 1], "dep_prios": [10, 5, 25, 10, 20, 5, 30, 30], "dependencies": ["jinja2.nodes", "jinja2.visitor", "jinja2.environment", "typing", "jinja2", "builtins", "_frozen_importlib", "abc"], "hash": "9d8182766f533ed54105e0d3a10b2eb1743874e3", "id": "jinja2.optimizer", "ignore_all": true, "interface_hash": "34ae183265e0c04969b34c7541151e8a42b8d491", "mtime": 1716993775, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\jinja2\\optimizer.py", "plugin_data": null, "size": 1651, "suppressed": [], "version_id": "1.15.0"}