import torch
import torch.nn as nn
from collections import defaultdict

# from zmq.backend.cffi import device

from buffer import Buffer
from config import config

# model.py 新增时序层
# self.temporal_layer = nn.LSTM(
#     input_size=hidden_dim//2,
#     hidden_size=128,
#     bidirectional=True
# )

# class SubjectAwareModel(nn.Module):
#     def __init__(self, input_dim=310, hidden_dim=64):
#         super().__init__()
#         # 共享特征提取层
#         self.feature_extractor = nn.Sequential(
#             nn.Linear(input_dim, hidden_dim),
#             nn.LayerNorm(hidden_dim),  # 增加批归一化
#             nn.GELU(),  # 改用GELU激活函数
#
#             nn.Linear(hidden_dim, hidden_dim * 2),
#             nn.LayerNorm(hidden_dim * 2),
#             nn.GELU(),
#             nn.Dropout(0.5),
#             nn.Linear(hidden_dim * 2, hidden_dim)  # 深度金字塔结构
#
#         )
#         # 每个受试者的独立分类头
#         self.heads = nn.ModuleDict()  # 动态添加
#
#     def add_subject_head(self, subject_id):
#         """为新增受试者创建分类头"""
#         new_head = nn.Linear(64,3).to(config.device)
#         print(f"[HEAD INIT] Subject{subject_id} device ;{new_head.weight.device}")
#         self.heads[str(subject_id)] = new_head  # 默认每个受试者3类情感
#
#     def forward(self, x, subject_id):
#         # [batch,62]
#         # print(f"Input x shape:{x.shape}")
#         device = next(self.parameters()).device
#         # print(f"[DEVICE CHECK] x.device:{x.device} | head.device:{self.heads[str(subject_id)].weight.device} | feat_extract.device:{device}")
#         features = self.feature_extractor(x)
#         # return self.heads[str(subject_id)](features)
#         # print(f"Features shape:{features.shape}")
#         # head = self.heads[str(subject_id)]
#         # print(f"Head weight shape: {head.weight.shape}")  # 应为 [3,64]
#         return self.heads[str(subject_id)](features)
class OCDNetWrapper(nn.Module):
    def __init__(self, input_dim=310, num_classes=3):
        super().__init__()
        # 加载OCDNet特征提取器（需适配时序输入）
        self.feature_extractor = OCDNetFeatureExtractor(input_dim)
        # 每个受试者的分类头
        self.heads = nn.ModuleDict()

    def add_subject_head(self, subject_id, hidden_dim=64):
        self.heads[str(subject_id)] = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Linear(hidden_dim * 2, 3)
        )

    def forward(self, x, subject_id):
        features = self.feature_extractor(x)  # [batch, hidden_dim]
        return self.heads[str(subject_id)](features)


# class ReplayBuffer:
#     def __init__(self, max_size):
#         self.max_size = max_size
#         self.buffer = []
#         self.subject_counts = defaultdict(int)  # 跟踪每个受试者的样本数
#
#     def add(self, features, labels, subject_id):
#         # 均匀采样，控制各受试者样本平衡
#         max_per_subject = self.max_size // (len(self.subject_counts) + 1)
#         new_samples = [
#             (f.cpu().numpy(), l.item(), subject_id)
#             for f, l in zip(features, labels)
#         ]
#         self.buffer.extend(new_samples)
#         # 超出容量时淘汰旧样本
#         if len(self.buffer) > self.max_size:
#             self.buffer = self.buffer[-self.max_size:]
#
#     def sample(self, batch_size):
#         indices = np.random.choice(len(self.buffer), batch_size)
#         samples = [self.buffer[i] for i in indices]
#         x = torch.FloatTensor([s[0] for s in samples])
#         y = torch.LongTensor([s[1] for s in samples])
#         s_ids = [s[2] for s in samples]
#         return x, y, s_ids
#
#     def remove_subject(self, subject_id):
#         self.buffer = [s for s in self.buffer if s[2] != subject_id]
class ContinualModel(nn.Module):
    def __init__(self, input_dim=62, hidden_dim=64, num_classes=3):
        super().__init__()
        # 特征提取器与之前相同
        self.feature_extractor = nn.Sequential(...)
        # 分类头初始化逻辑
        self.heads = nn.ModuleDict()

    def add_head(self, subject_id):
        self.heads[str(subject_id)] = nn.Linear(64, 3)