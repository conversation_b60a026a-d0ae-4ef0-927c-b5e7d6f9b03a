{".class": "MypyFile", "_fullname": "jinja2.debug", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CodeType": {".class": "SymbolTableNode", "cross_ref": "types.CodeType", "kind": "Gdef"}, "Context": {".class": "SymbolTableNode", "cross_ref": "jinja2.runtime.Context", "kind": "Gdef"}, "TemplateSyntaxError": {".class": "SymbolTableNode", "cross_ref": "jinja2.exceptions.TemplateSyntaxError", "kind": "Gdef"}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.debug.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.debug.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.debug.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.debug.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.debug.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.debug.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "fake_traceback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["exc_value", "tb", "filename", "lineno"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.debug.fake_traceback", "name": "fake_traceback", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["exc_value", "tb", "filename", "lineno"], "arg_types": ["builtins.BaseException", {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fake_traceback", "ret_type": "types.TracebackType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_template_locals": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["real_locals"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.debug.get_template_locals", "name": "get_template_locals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["real_locals"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_template_locals", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "internal_code": {".class": "SymbolTableNode", "cross_ref": "jinja2.utils.internal_code", "kind": "Gdef"}, "missing": {".class": "SymbolTableNode", "cross_ref": "jinja2.utils.missing", "kind": "Gdef"}, "rewrite_traceback_stack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["source"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.debug.rewrite_traceback_stack", "name": "rewrite_traceback_stack", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["source"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rewrite_traceback_stack", "ret_type": "builtins.BaseException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\jinja2\\debug.py"}