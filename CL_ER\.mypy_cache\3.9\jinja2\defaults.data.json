{".class": "MypyFile", "_fullname": "jinja2.defaults", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BLOCK_END_STRING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "jinja2.defaults.BLOCK_END_STRING", "name": "BLOCK_END_STRING", "type": "builtins.str"}}, "BLOCK_START_STRING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "jinja2.defaults.BLOCK_START_STRING", "name": "BLOCK_START_STRING", "type": "builtins.str"}}, "COMMENT_END_STRING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "jinja2.defaults.COMMENT_END_STRING", "name": "COMMENT_END_STRING", "type": "builtins.str"}}, "COMMENT_START_STRING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "jinja2.defaults.COMMENT_START_STRING", "name": "COMMENT_START_STRING", "type": "builtins.str"}}, "Cycler": {".class": "SymbolTableNode", "cross_ref": "jinja2.utils.Cycler", "kind": "Gdef"}, "DEFAULT_FILTERS": {".class": "SymbolTableNode", "cross_ref": "jinja2.filters.FILTERS", "kind": "Gdef"}, "DEFAULT_NAMESPACE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "jinja2.defaults.DEFAULT_NAMESPACE", "name": "DEFAULT_NAMESPACE", "type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "DEFAULT_POLICIES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "jinja2.defaults.DEFAULT_POLICIES", "name": "DEFAULT_POLICIES", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "DEFAULT_TESTS": {".class": "SymbolTableNode", "cross_ref": "jinja2.tests.TESTS", "kind": "Gdef"}, "Joiner": {".class": "SymbolTableNode", "cross_ref": "jinja2.utils.Joiner", "kind": "Gdef"}, "KEEP_TRAILING_NEWLINE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "jinja2.defaults.KEEP_TRAILING_NEWLINE", "name": "KEEP_TRAILING_NEWLINE", "type": "builtins.bool"}}, "LINE_COMMENT_PREFIX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "jinja2.defaults.LINE_COMMENT_PREFIX", "name": "LINE_COMMENT_PREFIX", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "LINE_STATEMENT_PREFIX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "jinja2.defaults.LINE_STATEMENT_PREFIX", "name": "LINE_STATEMENT_PREFIX", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "LSTRIP_BLOCKS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "jinja2.defaults.LSTRIP_BLOCKS", "name": "LSTRIP_BLOCKS", "type": "builtins.bool"}}, "NEWLINE_SEQUENCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "jinja2.defaults.NEWLINE_SEQUENCE", "name": "NEWLINE_SEQUENCE", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "\n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "\r\n"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "\r"}], "uses_pep604_syntax": false}}}, "Namespace": {".class": "SymbolTableNode", "cross_ref": "jinja2.utils.Namespace", "kind": "Gdef"}, "TRIM_BLOCKS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "jinja2.defaults.TRIM_BLOCKS", "name": "TRIM_BLOCKS", "type": "builtins.bool"}}, "VARIABLE_END_STRING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "jinja2.defaults.VARIABLE_END_STRING", "name": "VARIABLE_END_STRING", "type": "builtins.str"}}, "VARIABLE_START_STRING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "jinja2.defaults.VARIABLE_START_STRING", "name": "VARIABLE_START_STRING", "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.defaults.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.defaults.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.defaults.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.defaults.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.defaults.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.defaults.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "generate_lorem_ipsum": {".class": "SymbolTableNode", "cross_ref": "jinja2.utils.generate_lorem_ipsum", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "te": {".class": "SymbolTableNode", "cross_ref": "typing_extensions", "kind": "Gdef"}}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\jinja2\\defaults.py"}