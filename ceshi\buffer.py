# Copyright 2020-present, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>.
# All rights reserved.
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

import torch
import numpy as np
from collections import defaultdict
from typing import Tuple
from torchvision import transforms


def reservoir(num_seen_examples: int, buffer_size: int) -> int:
    """
    Reservoir sampling algorithm.
    :param num_seen_examples: the number of seen examples
    :param buffer_size: the maximum buffer size
    :return: the target index if the current image is sampled, else -1
    """
    if num_seen_examples < buffer_size:
        return num_seen_examples

    rand = np.random.randint(0, num_seen_examples + 1)
    if rand < buffer_size:
        return rand
    else:
        return -1


def ring(num_seen_examples: int, buffer_portion_size: int, task: int) -> int:
    return num_seen_examples % buffer_portion_size + task * buffer_portion_size


class Buffer:
    """
    The memory buffer of rehearsal method.
    """
    def __init__(self, buffer_size, device, n_tasks=None, mode='reservoir'):
        assert mode in ['ring', 'reservoir']
        self.buffer_size = buffer_size
        self.device = device
        self.num_seen_examples = 0
        self.functional_index = eval(mode)
        if mode == 'ring':
            assert n_tasks is not None
            self.task_number = n_tasks
            self.buffer_portion_size = buffer_size // n_tasks
        self.attributes = ['examples', 'labels', 'logits', 'task_labels']
        # 新增数据结构：基于task_labels（受试者ID）快速索引数据
        self.task_indices = defaultdict(list)  # {'subject_id': [buffer_idx1, buffer_idx2...]}

    def init_tensors(self, examples: torch.Tensor, labels: torch.Tensor,
                     logits: torch.Tensor, task_labels: torch.Tensor) -> None:
        """
        Initializes just the required tensors.
        :param examples: tensor containing the images
        :param labels: tensor containing the labels
        :param logits: tensor containing the outputs of the network
        :param task_labels: tensor containing the task labels
        """
        for attr_str in self.attributes:
            attr = eval(attr_str)
            if attr is not None and not hasattr(self, attr_str):
                typ = torch.int64 if attr_str.endswith('els') else torch.float32
                setattr(self, attr_str, torch.zeros((self.buffer_size,
                        *attr.shape[1:]), dtype=typ, device=self.device))
    


    def add_data(self, examples, labels=None, logits=None, task_labels=None):
        """
        Adds the data to the memory buffer according to the reservoir strategy.
        :param examples: tensor containing the images
        :param labels: tensor containing the labels
        :param logits: tensor containing the outputs of the network
        :param task_labels: tensor containing the task labels
        :return:
        """
        if not hasattr(self, 'examples'):
            self.init_tensors(examples, labels, logits, task_labels)

        for i in range(examples.shape[0]):
            index = reservoir(self.num_seen_examples, self.buffer_size)
            self.num_seen_examples += 1
            # if index >= 0:
            #     self.examples[index] = examples[i].to(self.device)
            #     if labels is not None:
            #         self.labels[index] = labels[i].to(self.device)
            #     if logits is not None:
            #         self.logits[index] = logits[i].to(self.device)
            #     if task_labels is not None:
            #         self.task_labels[index] = task_labels[i].to(self.device)
            # ============ 核心修改点：动态维护任务索引 ============
            if index >= 0:
                # 1. 替换旧数据时，先删除旧任务索引
                if hasattr(self, 'task_labels') and index < len(self.task_labels):
                    old_subject = int(self.task_labels[index].item())
                    if index in self.task_indices[old_subject]:
                        self.task_indices[old_subject].remove(index)

                # 2. 添加新数据并更新索引
                self.examples[index] = examples[i].to(self.device)
                if labels is not None:
                    self.labels[index] = labels[i].to(self.device)
                if logits is not None:
                    self.logits[index] = logits[i].to(self.device)
                if task_labels is not None:
                    sub_id = int(task_labels[i].item())
                    self.task_labels[index] = task_labels[i].to(self.device)
                    self.task_indices[sub_id].append(index)  # 新增

    def remove_data(self, example_to_remove, label_to_remove):
        indices_to_remove = []
        for i, ex in enumerate(self.examples):
            if (ex == example_to_remove).all() and self.labels[i] == label_to_remove:
                indices_to_remove.append(i)
        indices_to_keep = [i for i in range(self.examples.shape[0]) if i not in indices_to_remove]
        self.examples = self.examples[indices_to_keep]
        self.labels = self.labels[indices_to_keep]

    
    #def remove_class(self, class):
        # TODO - Romit
        #pass

    def remove_ids(self, ids):
        # Step1: Modify the buffer to stores unique ids of samples as well.
        # steps2: Take a list of ids to remove
        # Remove the given ids
        pass


    def remove_task(self, task):
        print(f"Removing Task: {task+1} from the buffer")
        print(f"Number of sampels in buffer beofre removal: {len(self.examples)}")
        
        indices_to_remove = []

        for i, ex in enumerate(self.task_labels):
            if ex == task:
                indices_to_remove.append(i)

        print(f"Number of samples selectted to be removed: {len(indices_to_remove)}")
        
        indices_to_keep = [i for i in range(self.examples.shape[0]) if i not in indices_to_remove]
        self.examples[:len(indices_to_keep)] = self.examples[indices_to_keep]
        self.labels[:len(indices_to_keep)] = self.labels[indices_to_keep]
        self.task_labels[:len(indices_to_keep)] = self.task_labels[indices_to_keep]
        self.examples[len(indices_to_keep):] = 0
        self.labels[len(indices_to_keep):] = 0
        self.task_labels[len(indices_to_keep):] = 0
        print(f"Number of samples in buffer after removal: {len(self.examples)}")
        self.num_seen_examples -= len(indices_to_remove)

    def get_data(self, size: int, transform: transforms = None, exclude_subjects=[]) -> Tuple:
    # def get_data(self, size: int, transform: transforms=None) -> Tuple:
        """
        Random samples a batch of size items.
        :param size: the number of requested items
        :param transform: the transformation to be applied (data augmentation)
        :return:
        """
        # if size > min(self.num_seen_examples, self.examples.shape[0]):
        #     size = min(self.num_seen_examples, self.examples.shape[0])
        #
        # choice = np.random.choice(min(self.num_seen_examples, self.examples.shape[0]),
        #                           size=size, replace=False)
        valid_indices = []
        for sub_id, indices in self.task_indices.items():
            if sub_id in exclude_subjects:
                continue  # 跳过被排除的受试者
            valid_indices.extend(indices)

        if len(valid_indices) == 0:
            return None  # 无可用数据

        # 随机选择指定大小的索引
        choice = np.random.choice(valid_indices, size=min(size, len(valid_indices)), replace=False)
        if transform is None: transform = lambda x: x
        ret_tuple = (torch.stack([transform(ee.cpu())
                            for ee in self.examples[choice]]).to(self.device),)
        for attr_str in self.attributes[1:]:
            if hasattr(self, attr_str):
                attr = getattr(self, attr_str)
                ret_tuple += (attr[choice],)

        return ret_tuple

    def is_empty(self) -> bool:
        """
        Returns true if the buffer is empty, false otherwise.
        """
        if self.num_seen_examples == 0:
            return True
        else:
            return False

    def get_all_data(self, transform: transforms=None) -> Tuple:
        """
        Return all the items in the memory buffer.
        :param transform: the transformation to be applied (data augmentation)
        :return: a tuple with all the items in the memory buffer
        """
        if transform is None: transform = lambda x: x
        ret_tuple = (torch.stack([transform(ee.cpu())
                            for ee in self.examples]).to(self.device),)
        for attr_str in self.attributes[1:]:
            if hasattr(self, attr_str):
                attr = getattr(self, attr_str)
                ret_tuple += (attr,)
        return ret_tuple

    def empty(self) -> None:
        """
        Set all the tensors to None.
        """
        for attr_str in self.attributes:
            if hasattr(self, attr_str):
                delattr(self, attr_str)
        self.num_seen_examples = 0
