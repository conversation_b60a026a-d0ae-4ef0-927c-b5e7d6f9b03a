{".class": "MypyFile", "_fullname": "models.resnet", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BasicBlock": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "models.resnet.BasicBlock", "name": "BasicBlock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "models.resnet.BasicBlock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "models.resnet", "mro": ["models.resnet.BasicBlock", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "in_planes", "planes", "stride"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.resnet.BasicBlock.__init__", "name": "__init__", "type": null}}, "bn1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.BasicBlock.bn1", "name": "bn1", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "bn2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.BasicBlock.bn2", "name": "bn2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "conv1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.BasicBlock.conv1", "name": "conv1", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "conv2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.BasicBlock.conv2", "name": "conv2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "expansion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "models.resnet.BasicBlock.expansion", "name": "expansion", "type": "builtins.int"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.resnet.BasicBlock.forward", "name": "forward", "type": null}}, "shortcut": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.BasicBlock.shortcut", "name": "shortcut", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "models.resnet.BasicBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "models.resnet.BasicBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Bottleneck": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "models.resnet.Bottleneck", "name": "Bottleneck", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "models.resnet.Bottleneck", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "models.resnet", "mro": ["models.resnet.Bottleneck", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "in_planes", "planes", "stride"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.resnet.Bottleneck.__init__", "name": "__init__", "type": null}}, "bn1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.Bottleneck.bn1", "name": "bn1", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "bn2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.Bottleneck.bn2", "name": "bn2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "bn3": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.Bottleneck.bn3", "name": "bn3", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "conv1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.Bottleneck.conv1", "name": "conv1", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "conv2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.Bottleneck.conv2", "name": "conv2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "conv3": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.Bottleneck.conv3", "name": "conv3", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "expansion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "models.resnet.Bottleneck.expansion", "name": "expansion", "type": "builtins.int"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.resnet.Bottleneck.forward", "name": "forward", "type": null}}, "shortcut": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.Bottleneck.shortcut", "name": "shortcut", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "models.resnet.Bottleneck.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "models.resnet.Bottleneck", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "F": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "models.resnet.F", "name": "F", "type": {".class": "AnyType", "missing_import_name": "models.resnet.F", "source_any": null, "type_of_any": 3}}}, "Reduced_ResNet18": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["nclasses", "nf", "bias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.resnet.Reduced_ResNet18", "name": "Reduced_ResNet18", "type": null}}, "ResNet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "models.resnet.ResNet", "name": "ResNet", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "models.resnet.ResNet", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "models.resnet", "mro": ["models.resnet.ResNet", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "block", "num_blocks", "num_classes", "nf", "bias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.resnet.ResNet.__init__", "name": "__init__", "type": null}}, "_make_layer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "block", "planes", "num_blocks", "stride"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.resnet.ResNet._make_layer", "name": "_make_layer", "type": null}}, "bn1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.ResNet.bn1", "name": "bn1", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "conv1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.ResNet.conv1", "name": "conv1", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.resnet.ResNet.features", "name": "features", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.resnet.ResNet.forward", "name": "forward", "type": null}}, "in_planes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.ResNet.in_planes", "name": "in_planes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.ResNet.layer1", "name": "layer1", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.ResNet.layer2", "name": "layer2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer3": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.ResNet.layer3", "name": "layer3", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer4": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.ResNet.layer4", "name": "layer4", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "linear": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.ResNet.linear", "name": "linear", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "logits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.resnet.ResNet.logits", "name": "logits", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "models.resnet.ResNet.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "models.resnet.ResNet", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResNet101": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["nclasses", "nf", "bias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.resnet.ResNet101", "name": "ResNet101", "type": null}}, "ResNet152": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["nclasses", "nf", "bias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.resnet.ResNet152", "name": "ResNet152", "type": null}}, "ResNet18": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["nclasses", "nf", "bias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.resnet.ResNet18", "name": "ResNet18", "type": null}}, "ResNet34": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["nclasses", "nf", "bias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.resnet.ResNet34", "name": "ResNet34", "type": null}}, "ResNet50": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["nclasses", "nf", "bias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.resnet.ResNet50", "name": "ResNet50", "type": null}}, "SupConResNet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "models.resnet.SupConResNet", "name": "SupConResNet", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "models.resnet.SupConResNet", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "models.resnet", "mro": ["models.resnet.SupConResNet", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "dim_in", "head", "feat_dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.resnet.SupConResNet.__init__", "name": "__init__", "type": null}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.SupConResNet.encoder", "name": "encoder", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.resnet.SupConResNet.features", "name": "features", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.resnet.SupConResNet.forward", "name": "forward", "type": null}}, "head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.resnet.SupConResNet.head", "name": "head", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "models.resnet.SupConResNet.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "models.resnet.SupConResNet", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "models.resnet.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "models.resnet.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "models.resnet.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "models.resnet.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "models.resnet.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "models.resnet.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "avg_pool2d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "models.resnet.avg_pool2d", "name": "avg_pool2d", "type": {".class": "AnyType", "missing_import_name": "models.resnet.avg_pool2d", "source_any": null, "type_of_any": 3}}}, "conv3x3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["in_planes", "out_planes", "stride"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.resnet.conv3x3", "name": "conv3x3", "type": null}}, "nn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "models.resnet.nn", "name": "nn", "type": {".class": "AnyType", "missing_import_name": "models.resnet.nn", "source_any": null, "type_of_any": 3}}}, "relu": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "models.resnet.relu", "name": "relu", "type": {".class": "AnyType", "missing_import_name": "models.resnet.relu", "source_any": null, "type_of_any": 3}}}}, "path": "D:\\continual_learning\\CL_ER\\models\\resnet.py"}