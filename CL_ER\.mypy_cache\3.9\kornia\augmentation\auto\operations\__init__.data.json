{".class": "MypyFile", "_fullname": "kornia.augmentation.auto.operations", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AutoContrast": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.AutoContrast", "kind": "Gdef"}, "Brightness": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Brightness", "kind": "Gdef"}, "Contrast": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Contrast", "kind": "Gdef"}, "Equalize": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Equalize", "kind": "Gdef"}, "Gray": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Gray", "kind": "Gdef"}, "HorizontalFlip": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.HorizontalFlip", "kind": "Gdef"}, "Hue": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Hue", "kind": "Gdef"}, "Invert": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Invert", "kind": "Gdef"}, "OperationBase": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.base.OperationBase", "kind": "Gdef"}, "PolicySequential": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.policy.PolicySequential", "kind": "Gdef"}, "Posterize": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Posterize", "kind": "Gdef"}, "Rotate": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Rotate", "kind": "Gdef"}, "Saturate": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Saturate", "kind": "Gdef"}, "Sharpness": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Sharpness", "kind": "Gdef"}, "ShearX": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.ShearX", "kind": "Gdef"}, "ShearY": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.ShearY", "kind": "Gdef"}, "Solarize": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Solarize", "kind": "Gdef"}, "SolarizeAdd": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.SolarizeAdd", "kind": "Gdef"}, "TranslateX": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.TranslateX", "kind": "Gdef"}, "TranslateY": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.TranslateY", "kind": "Gdef"}, "VerticalFlip": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.VerticalFlip", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\kornia\\augmentation\\auto\\operations\\__init__.py"}