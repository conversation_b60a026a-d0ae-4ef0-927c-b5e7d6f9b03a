import os
import pickle
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from config import config


# class SEEDDataset(Dataset):
#     def __init__(self, subject_id, session_ids, data_type='train'):
#         """
#         加载指定受试者在多会话的数据
#         :param subject_id: 受试者ID (1~15)
#         :param session_ids: 会话列表 (如[1,2,3])
#         :param data_type: 'train' 或 'test'
#         """
#
#         self.features = []
#         self.labels = []
#
#         # 遍历所有指定会话目录
#         for sess_id in session_ids:
#             sess_dir = os.path.join(config.data_root, f"session_{sess_id}")
#             # 解析文件名前缀
#             prefix = f"{subject_id}_"
#             files = [f for f in os.listdir(sess_dir) if f.startswith(prefix) and data_type in f]
#
#             for f in files:
#                 if '_dataset.pkl' in f:
#                     # 加载特征
#                     data_path = os.path.join(sess_dir, f)
#                     with open(data_path, "rb") as fp:
#                         features = pickle.load(fp)
#                         self.features.extend(features)
#                     # 加载标签
#                     label_path = data_path.replace("dataset", "labels")
#                     with open(label_path, "rb") as fp:
#                         labels = pickle.load(fp).flatten()
#                         self.labels.extend(labels)
#
#         # 转换为Numpy
#         self.features = np.array(self.features, dtype=np.float32)
#         self.labels = np.array(self.labels, dtype=np.int64)
#         self.features = self.features.reshape(len(self.features), -1)  # [N, 62*5]
#         self.mean = self.features.mean(axis=0)
#         self.std = self.features.std(axis=0)
#         self.features = (self.features - self.mean) / (self.std + 1e-8)  # 防止除零
#         print(f"[DEBUG] Final features shape: {self.features.shape}")  # 应为 [N,310]
#         print(
#             f"[DATA SANITY] 特征均值: {self.features.mean():.4f} | 方差: {self.features.std():.4f} | 标签分布: {np.bincount(self.labels)}")
        # 加载数据后立即打印特征维度
        # print(f"Loaded data shape: {self.features.shape}")  # 应该为 [N_samples, 62]
        # print(f"Sample feature dimension: {self.features[0].shape}")
# class SEEDDataset(Dataset):
#     def __init__(self, subject_id, session_ids, data_type='train'):
#         """适配新目录结构的数据加载"""
#         self.features = []
#         self.labels = []
#
#         # 遍历所有指定的会话和受试者目录
#         for sess_id in session_ids:
#             for sub_dir in os.listdir(os.path.join(config.data_root, f"session_{sess_id}")):
#                 if sub_dir.startswith("sub_") and str(subject_id) in sub_dir:
#                     sub_path = os.path.join(config.data_root, f"session_{sess_id}", sub_dir)
#
#                     # 查找符合条件的.pkl文件
#                     for f in os.listdir(sub_path):
#                         if f.endswith(f"_{data_type}.pkl"):
#                             data_path = os.path.join(sub_path, f)
#                             with open(data_path, "rb") as fp:
#                                 data, label = pickle.load(fp)  # 假设文件保存为(data, label)元组
#                                 self.features.extend(data)
#                                 self.labels.extend(label.flatten())
class SEEDDataset(Dataset):
    def __init__(self, subject_id, session_ids, data_type='train'):
        """适配新目录结构：data/label文件分离"""
        self.features = []
        self.labels = []

        # 遍历所有指定的会话和受试者目录 [!精准匹配文件名!]
        for sess_id in session_ids:
            session_dir = os.path.join(config.data_root, f"session_{sess_id}")
            sub_dir_name = f"sub_{subject_id}"  # 固定格式：sub_1, sub_2...
            sub_path = os.path.join(session_dir, sub_dir_name)

            if not os.path.exists(sub_path):
                continue  # 防错：目录不存在则跳过

            # 按data_type匹配对应文件
            for f in os.listdir(sub_path):
                prefix = f"session{sess_id}_sub{subject_id}_testTrial"  # 例如: session1_sub1_testTrial5_
                if f.endswith(f"_{data_type}_data.pkl") and f.startswith(prefix):
                    # 数据文件路径
                    data_path = os.path.join(sub_path, f)
                    # 对应标签文件路径（替换data为label）
                    label_path = data_path.replace("_data.pkl", "_label.pkl")

                    # 加载数据和标签
                    with open(data_path, "rb") as df, open(label_path, "rb") as lf:
                        features = pickle.load(df)
                        labels = pickle.load(lf).flatten()  # 确保标签展平
                        self.features.extend(features)
                        self.labels.extend(labels)

        # --- 数据验证与标准化 ---
        if len(self.features) == 0:
            raise ValueError(f"No {data_type} data found for Subject {subject_id} in sessions {session_ids}")
        # 数据标准化 [!根据你的要求选择是否保留!]
        self.features = np.array(self.features, dtype=np.float32)
        self.labels = np.array(self.labels, dtype=np.int64)
        self.features = (self.features - self.features.mean(axis=0)) / (self.features.std(axis=0) + 1e-8)
        if self.features.ndim == 1:
            self.features = self.features.reshape(-1, config.input_dim)  # [!匹配config输入维度!]
        elif self.features.shape[-1] != config.input_dim:
            raise ValueError(f"特征维度错误：预期 {config.input_dim}, 实际 {self.features.shape[-1]}")
            # 标准化（根据训练集统计）
        if data_type == 'train':
            self.mean = self.features.mean(axis=0)
            self.std = self.features.std(axis=0)
        self.features = (self.features - self.mean) / (self.std + 1e-8)

        print(f"[Data Loaded] Subject {subject_id} | {data_type.capitalize()} | Features: {self.features.shape} | Labels: {self.labels.shape}")

    def __len__(self):
        return len(self.features)

    def __getitem__(self, idx):
        feat = self.features[idx
        label = self.labels[idx]
        # 随机添加高斯噪声
        if self.data_type == 'train':
            noise = np.random.normal(0, 0.1, size=feat.shape)
            feat = feat + noise
            feat = self.features[idx][None, :]

    # return (
    #     torch.from_numpy(self.features[idx]).to(config.device),
    #     torch.from_numpy(np.array(self.labels[idx])).to(config.device)
    # )

    return (
        torch.FloatTensor(feat).to(config.device),
        torch.LongTensor([label]).to(config.device)
    )
        


def get_subject_loader(subject_id, session_ids=[1, 2, 3], data_type='train'):
    """生成指定受试者的数据加载器"""
    dataset = SEEDDataset(subject_id, session_ids, data_type)
    return DataLoader(
        dataset,
        batch_size=config.batch_size,
        shuffle=(data_type == 'train')
    )
