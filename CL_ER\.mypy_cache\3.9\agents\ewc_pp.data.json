{".class": "MypyFile", "_fullname": "agents.ewc_pp", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AverageMeter": {".class": "SymbolTableNode", "cross_ref": "utils.utils.AverageMeter", "kind": "Gdef"}, "ContinualLearner": {".class": "SymbolTableNode", "cross_ref": "agents.base.ContinualLearner", "kind": "Gdef"}, "EWC_pp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["agents.base.ContinualLearner"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "agents.ewc_pp.EWC_pp", "name": "EWC_pp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "agents.ewc_pp.EWC_pp", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "agents.ewc_pp", "mro": ["agents.ewc_pp.EWC_pp", "agents.base.ContinualLearner", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "models", "opt", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.ewc_pp.EWC_pp.__init__", "name": "__init__", "type": null}}, "accum_fisher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.ewc_pp.EWC_pp.accum_fisher", "name": "accum_fisher", "type": null}}, "alpha": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.ewc_pp.EWC_pp.alpha", "name": "alpha", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "eps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.ewc_pp.EWC_pp.eps", "name": "eps", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "fisher_update_after": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.ewc_pp.EWC_pp.fisher_update_after", "name": "fisher_update_after", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "grad_clip_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.ewc_pp.EWC_pp.grad_clip_norm", "name": "grad_clip_norm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "init_fisher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.ewc_pp.EWC_pp.init_fisher", "name": "init_fisher", "type": null}}, "lambda_": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.ewc_pp.EWC_pp.lambda_", "name": "lambda_", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "normalize_fisher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.ewc_pp.EWC_pp.normalize_fisher", "name": "normalize_fisher", "type": null}}, "normalized_fisher": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.ewc_pp.EWC_pp.normalized_fisher", "name": "normalized_fisher", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "prev_params": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.ewc_pp.EWC_pp.prev_params", "name": "prev_params", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "running_fisher": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.ewc_pp.EWC_pp.running_fisher", "name": "running_fisher", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "tmp_fisher": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.ewc_pp.EWC_pp.tmp_fisher", "name": "tmp_fisher", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "total_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "inputs", "targets"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.ewc_pp.EWC_pp.total_loss", "name": "total_loss", "type": null}}, "train_learner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x_train", "y_train"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.ewc_pp.EWC_pp.train_learner", "name": "train_learner", "type": null}}, "update_running_fisher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.ewc_pp.EWC_pp.update_running_fisher", "name": "update_running_fisher", "type": null}}, "weights": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.ewc_pp.EWC_pp.weights", "name": "weights", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "agents.ewc_pp.EWC_pp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "agents.ewc_pp.EWC_pp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.ewc_pp.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.ewc_pp.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.ewc_pp.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.ewc_pp.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.ewc_pp.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.ewc_pp.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "data": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data", "kind": "Gdef"}, "dataset_transform": {".class": "SymbolTableNode", "cross_ref": "continuum.data_utils.dataset_transform", "kind": "Gdef"}, "maybe_cuda": {".class": "SymbolTableNode", "cross_ref": "utils.utils.maybe_cuda", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "transforms_match": {".class": "SymbolTableNode", "cross_ref": "utils.setup_elements.transforms_match", "kind": "Gdef"}}, "path": "D:\\continual_learning\\CL_ER\\agents\\ewc_pp.py"}