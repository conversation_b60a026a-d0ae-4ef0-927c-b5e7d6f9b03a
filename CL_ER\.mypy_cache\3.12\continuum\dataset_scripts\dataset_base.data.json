{".class": "MypyFile", "_fullname": "continuum.dataset_scripts.dataset_base", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "DatasetBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["download_load", 1], ["new_run", 1], ["new_task", 1], ["setup", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase", "name": "DatasetBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "continuum.dataset_scripts.dataset_base", "mro": ["continuum.dataset_scripts.dataset_base.DatasetBase", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "dataset", "scenario", "task_nums", "run", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.__init__", "name": "__init__", "type": null}}, "_is_properly_setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase._is_properly_setup", "name": "_is_properly_setup", "type": null}}, "clean_mem_test_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.clean_mem_test_set", "name": "clean_mem_test_set", "type": null}}, "dataset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.dataset", "name": "dataset", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dataset_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.dataset_info", "name": "dataset_info", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.dataset_info", "name": "dataset_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["continuum.dataset_scripts.dataset_base.DatasetBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dataset_info of DatasetBase", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "download_load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.download_load", "name": "download_load", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.download_load", "name": "download_load", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["continuum.dataset_scripts.dataset_base.DatasetBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "download_load of DatasetBase", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_test_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.get_test_set", "name": "get_test_set", "type": null}}, "new_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.new_run", "name": "new_run", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.new_run", "name": "new_run", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["continuum.dataset_scripts.dataset_base.DatasetBase", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_run of DatasetBase", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "new_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 4], "arg_names": ["self", "cur_task", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.new_task", "name": "new_task", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.new_task", "name": "new_task", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "cur_task", "kwargs"], "arg_types": ["continuum.dataset_scripts.dataset_base.DatasetBase", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_task of DatasetBase", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "params": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.params", "name": "params", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "root": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.root", "name": "root", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "run": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.run", "name": "run", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "scenario": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.scenario", "name": "scenario", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.setup", "name": "setup", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.setup", "name": "setup", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["continuum.dataset_scripts.dataset_base.DatasetBase", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup of DatasetBase", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "task_nums": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.task_nums", "name": "task_nums", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "test_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.test_data", "name": "test_data", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "test_label": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.test_label", "name": "test_label", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "test_set": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.test_set", "name": "test_set", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "val_set": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.val_set", "name": "val_set", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "continuum.dataset_scripts.dataset_base.DatasetBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "continuum.dataset_scripts.dataset_base.DatasetBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.dataset_scripts.dataset_base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.dataset_scripts.dataset_base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.dataset_scripts.dataset_base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.dataset_scripts.dataset_base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.dataset_scripts.dataset_base.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.dataset_scripts.dataset_base.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}}, "path": "D:\\continual_learning\\CL_ER\\continuum\\dataset_scripts\\dataset_base.py"}