{".class": "MypyFile", "_fullname": "utils.global_vars", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "MODELS_NDPM_CLASSIFIER_CLS_NF_BASE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_CLASSIFIER_CLS_NF_BASE", "name": "MODELS_NDPM_CLASSIFIER_CLS_NF_BASE", "type": "builtins.int"}}, "MODELS_NDPM_CLASSIFIER_CLS_NF_EXT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_CLASSIFIER_CLS_NF_EXT", "name": "MODELS_NDPM_CLASSIFIER_CLS_NF_EXT", "type": "builtins.int"}}, "MODELS_NDPM_CLASSIFIER_NORM_LAYER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_CLASSIFIER_NORM_LAYER", "name": "MODELS_NDPM_CLASSIFIER_NORM_LAYER", "type": "builtins.str"}}, "MODELS_NDPM_CLASSIFIER_NUM_BLOCKS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_CLASSIFIER_NUM_BLOCKS", "name": "MODELS_NDPM_CLASSIFIER_NUM_BLOCKS", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "MODELS_NDPM_COMPONENT_CLIP_GRAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_COMPONENT_CLIP_GRAD", "name": "MODELS_NDPM_COMPONENT_CLIP_GRAD", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "MODELS_NDPM_COMPONENT_LR_SCHEDULER_D": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_COMPONENT_LR_SCHEDULER_D", "name": "MODELS_NDPM_COMPONENT_LR_SCHEDULER_D", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "MODELS_NDPM_COMPONENT_LR_SCHEDULER_G": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_COMPONENT_LR_SCHEDULER_G", "name": "MODELS_NDPM_COMPONENT_LR_SCHEDULER_G", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "MODELS_NDPM_NDPM_DISABLE_D": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_NDPM_DISABLE_D", "name": "MODELS_NDPM_NDPM_DISABLE_D", "type": "builtins.bool"}}, "MODELS_NDPM_NDPM_IMPLICIT_LR_DECAY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_NDPM_IMPLICIT_LR_DECAY", "name": "MODELS_NDPM_NDPM_IMPLICIT_LR_DECAY", "type": "builtins.bool"}}, "MODELS_NDPM_NDPM_SEND_TO_STM_ALWAYS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_NDPM_SEND_TO_STM_ALWAYS", "name": "MODELS_NDPM_NDPM_SEND_TO_STM_ALWAYS", "type": "builtins.bool"}}, "MODELS_NDPM_NDPM_SLEEP_BATCH_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_NDPM_SLEEP_BATCH_SIZE", "name": "MODELS_NDPM_NDPM_SLEEP_BATCH_SIZE", "type": "builtins.int"}}, "MODELS_NDPM_NDPM_SLEEP_NUM_WORKERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_NDPM_SLEEP_NUM_WORKERS", "name": "MODELS_NDPM_NDPM_SLEEP_NUM_WORKERS", "type": "builtins.int"}}, "MODELS_NDPM_NDPM_SLEEP_SLEEP_VAL_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_NDPM_SLEEP_SLEEP_VAL_SIZE", "name": "MODELS_NDPM_NDPM_SLEEP_SLEEP_VAL_SIZE", "type": "builtins.int"}}, "MODELS_NDPM_NDPM_SLEEP_STEP_D": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_NDPM_SLEEP_STEP_D", "name": "MODELS_NDPM_NDPM_SLEEP_STEP_D", "type": "builtins.int"}}, "MODELS_NDPM_NDPM_SLEEP_STEP_G": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_NDPM_SLEEP_STEP_G", "name": "MODELS_NDPM_NDPM_SLEEP_STEP_G", "type": "builtins.int"}}, "MODELS_NDPM_NDPM_SLEEP_SUMMARY_STEP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_NDPM_SLEEP_SUMMARY_STEP", "name": "MODELS_NDPM_NDPM_SLEEP_SUMMARY_STEP", "type": "builtins.int"}}, "MODELS_NDPM_NDPM_WEIGHT_DECAY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_NDPM_WEIGHT_DECAY", "name": "MODELS_NDPM_NDPM_WEIGHT_DECAY", "type": "builtins.float"}}, "MODELS_NDPM_VAE_LEARN_X_LOG_VAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_VAE_LEARN_X_LOG_VAR", "name": "MODELS_NDPM_VAE_LEARN_X_LOG_VAR", "type": "builtins.bool"}}, "MODELS_NDPM_VAE_NF_EXT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_VAE_NF_EXT", "name": "MODELS_NDPM_VAE_NF_EXT", "type": "builtins.int"}}, "MODELS_NDPM_VAE_PRECURSOR_CONDITIONED_DECODER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_VAE_PRECURSOR_CONDITIONED_DECODER", "name": "MODELS_NDPM_VAE_PRECURSOR_CONDITIONED_DECODER", "type": "builtins.bool"}}, "MODELS_NDPM_VAE_RECON_LOSS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_VAE_RECON_LOSS", "name": "MODELS_NDPM_VAE_RECON_LOSS", "type": "builtins.str"}}, "MODELS_NDPM_VAE_X_LOG_VAR_PARAM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_VAE_X_LOG_VAR_PARAM", "name": "MODELS_NDPM_VAE_X_LOG_VAR_PARAM", "type": "builtins.int"}}, "MODELS_NDPM_VAE_Z_DIM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_VAE_Z_DIM", "name": "MODELS_NDPM_VAE_Z_DIM", "type": "builtins.int"}}, "MODELS_NDPM_VAE_Z_SAMPLES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODELS_NDPM_VAE_Z_SAMPLES", "name": "MODELS_NDPM_VAE_Z_SAMPLES", "type": "builtins.int"}}, "MODLES_NDPM_VAE_NF_BASE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "utils.global_vars.MODLES_NDPM_VAE_NF_BASE", "name": "MODLES_NDPM_VAE_NF_BASE", "type": "builtins.int"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.global_vars.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.global_vars.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.global_vars.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.global_vars.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.global_vars.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.global_vars.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "D:\\continual_learning\\CL_ER\\utils\\global_vars.py"}