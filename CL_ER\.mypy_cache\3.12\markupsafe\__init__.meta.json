{"data_mtime": 1754286232, "dep_lines": [302, 298, 1, 2, 3, 4, 5, 8, 154, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 10, 25, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["markupsafe._native", "markupsafe._speedups", "functools", "re", "string", "sys", "typing", "typing_extensions", "html", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum"], "hash": "1c4165b975efcbe2819a59fb44ee65f00772ff12", "id": "markupsafe", "ignore_all": true, "interface_hash": "eb7a226d0353c812604bc41003e284494a0b7287", "mtime": 1707425795, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "d:\\anaconda3\\Lib\\site-packages\\markupsafe\\__init__.py", "plugin_data": null, "size": 10338, "suppressed": [], "version_id": "1.15.0"}