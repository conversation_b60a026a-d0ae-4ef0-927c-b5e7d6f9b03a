{".class": "MypyFile", "_fullname": "ntpath", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AnyStr": {".class": "SymbolTableNode", "cross_ref": "typing.AnyStr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BytesPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.BytesPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LiteralString": {".class": "SymbolTableNode", "cross_ref": "typing.LiteralString", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PathLike": {".class": "SymbolTableNode", "cross_ref": "os.PathLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StrOrBytesPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.StrOrBytesPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StrPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.StrPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "ntpath.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ntpath.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ntpath.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ntpath.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ntpath.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ntpath.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ntpath.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abspath": {".class": "SymbolTableNode", "cross_ref": "posixpath.abspath", "kind": "Gdef"}, "altsep": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ntpath.altsep", "name": "altsep", "type": "builtins.str"}}, "basename": {".class": "SymbolTableNode", "cross_ref": "posixpath.basename", "kind": "Gdef"}, "commonpath": {".class": "SymbolTableNode", "cross_ref": "posixpath.commonpath", "kind": "Gdef"}, "commonprefix": {".class": "SymbolTableNode", "cross_ref": "genericpath.commonprefix", "kind": "Gdef"}, "curdir": {".class": "SymbolTableNode", "cross_ref": "posixpath.curdir", "kind": "Gdef"}, "defpath": {".class": "SymbolTableNode", "cross_ref": "posixpath.defpath", "kind": "Gdef"}, "devnull": {".class": "SymbolTableNode", "cross_ref": "posixpath.devnull", "kind": "Gdef"}, "dirname": {".class": "SymbolTableNode", "cross_ref": "posixpath.dirname", "kind": "Gdef"}, "exists": {".class": "SymbolTableNode", "cross_ref": "genericpath.exists", "kind": "Gdef"}, "expanduser": {".class": "SymbolTableNode", "cross_ref": "posixpath.expanduser", "kind": "Gdef"}, "expandvars": {".class": "SymbolTableNode", "cross_ref": "posixpath.expandvars", "kind": "Gdef"}, "extsep": {".class": "SymbolTableNode", "cross_ref": "posixpath.extsep", "kind": "Gdef"}, "getatime": {".class": "SymbolTableNode", "cross_ref": "genericpath.getatime", "kind": "Gdef"}, "getctime": {".class": "SymbolTableNode", "cross_ref": "genericpath.getctime", "kind": "Gdef"}, "getmtime": {".class": "SymbolTableNode", "cross_ref": "genericpath.getmtime", "kind": "Gdef"}, "getsize": {".class": "SymbolTableNode", "cross_ref": "genericpath.getsize", "kind": "Gdef"}, "isabs": {".class": "SymbolTableNode", "cross_ref": "posixpath.isabs", "kind": "Gdef"}, "isdir": {".class": "SymbolTableNode", "cross_ref": "genericpath.isdir", "kind": "Gdef"}, "isfile": {".class": "SymbolTableNode", "cross_ref": "genericpath.isfile", "kind": "Gdef"}, "isjunction": {".class": "SymbolTableNode", "cross_ref": "posixpath.isjunction", "kind": "Gdef"}, "islink": {".class": "SymbolTableNode", "cross_ref": "posixpath.islink", "kind": "Gdef"}, "ismount": {".class": "SymbolTableNode", "cross_ref": "posixpath.ismount", "kind": "Gdef"}, "join": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "ntpath.join", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, "paths"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ntpath.join", "name": "join", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, "paths"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "join", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ntpath.join", "name": "join", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, "paths"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "join", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, "paths"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ntpath.join", "name": "join", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, "paths"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "join", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ntpath.join", "name": "join", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, "paths"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "join", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, "paths"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ntpath.join", "name": "join", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, "paths"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.BytesPath"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.BytesPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "join", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ntpath.join", "name": "join", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, "paths"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.BytesPath"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.BytesPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "join", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, "paths"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "join", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, "paths"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "join", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, "paths"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.BytesPath"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.BytesPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "join", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "lexists": {".class": "SymbolTableNode", "cross_ref": "posixpath.lexists", "kind": "Gdef"}, "normcase": {".class": "SymbolTableNode", "cross_ref": "posixpath.normcase", "kind": "Gdef"}, "normpath": {".class": "SymbolTableNode", "cross_ref": "posixpath.normpath", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "pardir": {".class": "SymbolTableNode", "cross_ref": "posixpath.pardir", "kind": "Gdef"}, "pathsep": {".class": "SymbolTableNode", "cross_ref": "posixpath.pathsep", "kind": "Gdef"}, "realpath": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "ntpath.realpath", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["path", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ntpath.realpath", "name": "realpath", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["path", "strict"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "ntpath.realpath#0", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "os.PathLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "realpath", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "ntpath.realpath#0", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "ntpath.realpath#0", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ntpath.realpath", "name": "realpath", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["path", "strict"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "ntpath.realpath#0", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "os.PathLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "realpath", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "ntpath.realpath#0", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "ntpath.realpath#0", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["path", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "ntpath.realpath", "name": "realpath", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["path", "strict"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "ntpath.realpath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "realpath", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "ntpath.realpath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "ntpath.realpath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "ntpath.realpath", "name": "realpath", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["path", "strict"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "ntpath.realpath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "realpath", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "ntpath.realpath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "ntpath.realpath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["path", "strict"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "ntpath.realpath#0", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "os.PathLike"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "realpath", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "ntpath.realpath#0", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "ntpath.realpath#0", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["path", "strict"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "ntpath.realpath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "realpath", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "ntpath.realpath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "ntpath.realpath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}]}}}, "relpath": {".class": "SymbolTableNode", "cross_ref": "posixpath.relpath", "kind": "Gdef"}, "samefile": {".class": "SymbolTableNode", "cross_ref": "genericpath.samefile", "kind": "Gdef"}, "sameopenfile": {".class": "SymbolTableNode", "cross_ref": "genericpath.sameopenfile", "kind": "Gdef"}, "samestat": {".class": "SymbolTableNode", "cross_ref": "genericpath.samestat", "kind": "Gdef"}, "sep": {".class": "SymbolTableNode", "cross_ref": "posixpath.sep", "kind": "Gdef"}, "split": {".class": "SymbolTableNode", "cross_ref": "posixpath.split", "kind": "Gdef"}, "splitdrive": {".class": "SymbolTableNode", "cross_ref": "posixpath.splitdrive", "kind": "Gdef"}, "splitext": {".class": "SymbolTableNode", "cross_ref": "posixpath.splitext", "kind": "Gdef"}, "splitroot": {".class": "SymbolTableNode", "cross_ref": "posixpath.splitroot", "kind": "Gdef"}, "supports_unicode_filenames": {".class": "SymbolTableNode", "cross_ref": "posixpath.supports_unicode_filenames", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\ntpath.pyi"}