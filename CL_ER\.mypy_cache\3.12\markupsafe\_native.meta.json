{"data_mtime": 1754286231, "dep_lines": [1, 3, 1, 1, 1], "dep_prios": [10, 5, 5, 30, 30], "dependencies": ["typing", "markupsafe", "builtins", "_frozen_importlib", "abc"], "hash": "8b2d92a82dbd3695eb1a62c44cd8a3b8a5aba586", "id": "markupsafe._native", "ignore_all": true, "interface_hash": "21c7e2052a0f60653f7cdee0d312e3ca7e0c4031", "mtime": 1707425795, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "d:\\anaconda3\\Lib\\site-packages\\markupsafe\\_native.py", "plugin_data": null, "size": 1713, "suppressed": [], "version_id": "1.15.0"}