{"data_mtime": 1754286232, "dep_lines": [6, 7, 1, 4, 1, 1, 3, 2], "dep_prios": [5, 5, 5, 5, 5, 30, 10, 10], "dependencies": ["utils.utils", "utils.global_vars", "abc", "typing", "builtins", "_frozen_importlib"], "hash": "4088ec66892834f09a14f59d5d58571c42b7dc77", "id": "models.ndpm.component", "ignore_all": true, "interface_hash": "9a7c70450d6353e1bb8d8293024494caa30bba14", "mtime": 1751894924, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\models\\ndpm\\component.py", "plugin_data": null, "size": 3950, "suppressed": ["torch.nn", "torch"], "version_id": "1.15.0"}