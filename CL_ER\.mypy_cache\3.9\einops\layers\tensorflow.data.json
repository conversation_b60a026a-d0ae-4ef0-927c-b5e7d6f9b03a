{".class": "MypyFile", "_fullname": "einops.layers.tensorflow", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EinMix": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["einops.layers._einmix._EinmixMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops.layers.tensorflow.EinMix", "name": "EinMix", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "einops.layers.tensorflow.EinMix", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops.layers.tensorflow", "mro": ["einops.layers.tensorflow.EinMix", "einops.layers._einmix._EinmixMixin", "builtins.object"], "names": {".class": "SymbolTable", "_create_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "weight_shape", "weight_bound", "bias_shape", "bias_bound"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops.layers.tensorflow.EinMix._create_parameters", "name": "_create_parameters", "type": null}}, "_create_rearrange_layers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "pre_reshape_pattern", "pre_reshape_lengths", "post_reshape_pattern", "post_reshape_lengths"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops.layers.tensorflow.EinMix._create_rearrange_layers", "name": "_create_rearrange_layers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "pre_reshape_pattern", "pre_reshape_lengths", "post_reshape_pattern", "post_reshape_lengths"], "arg_types": ["einops.layers.tensorflow.EinMix", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_rearrange_layers of EinMix", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_params": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops.layers.tensorflow.EinMix._params", "name": "_params", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops.layers.tensorflow.EinMix.bias", "name": "bias", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops.layers.tensorflow.EinMix.build", "name": "build", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops.layers.tensorflow.EinMix.call", "name": "call", "type": null}}, "get_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops.layers.tensorflow.EinMix.get_config", "name": "get_config", "type": null}}, "post_rearrange": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops.layers.tensorflow.EinMix.post_rearrange", "name": "post_rearrange", "type": {".class": "UnionType", "items": ["einops.layers.tensorflow.Rearrange", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "pre_rearrange": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops.layers.tensorflow.EinMix.pre_rearrange", "name": "pre_rearrange", "type": {".class": "UnionType", "items": ["einops.layers.tensorflow.Rearrange", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops.layers.tensorflow.EinMix.weight", "name": "weight", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops.layers.tensorflow.EinMix.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops.layers.tensorflow.EinMix", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Layer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "einops.layers.tensorflow.Layer", "name": "Layer", "type": {".class": "AnyType", "missing_import_name": "einops.layers.tensorflow.Layer", "source_any": null, "type_of_any": 3}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Rearrange": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["einops.layers.RearrangeMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops.layers.tensorflow.Rearrange", "name": "Rearrange", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "einops.layers.tensorflow.Rearrange", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops.layers.tensorflow", "mro": ["einops.layers.tensorflow.Rearrange", "einops.layers.RearrangeMixin", "builtins.object"], "names": {".class": "SymbolTable", "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops.layers.tensorflow.Rearrange.build", "name": "build", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops.layers.tensorflow.Rearrange.call", "name": "call", "type": null}}, "get_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops.layers.tensorflow.Rearrange.get_config", "name": "get_config", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops.layers.tensorflow.Rearrange.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops.layers.tensorflow.Rearrange", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RearrangeMixin": {".class": "SymbolTableNode", "cross_ref": "einops.layers.RearrangeMixin", "kind": "Gdef"}, "Reduce": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["einops.layers.ReduceMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops.layers.tensorflow.Reduce", "name": "Reduce", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "einops.layers.tensorflow.Reduce", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops.layers.tensorflow", "mro": ["einops.layers.tensorflow.Reduce", "einops.layers.ReduceMixin", "builtins.object"], "names": {".class": "SymbolTable", "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops.layers.tensorflow.Reduce.build", "name": "build", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops.layers.tensorflow.Reduce.call", "name": "call", "type": null}}, "get_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops.layers.tensorflow.Reduce.get_config", "name": "get_config", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops.layers.tensorflow.Reduce.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops.layers.tensorflow.Reduce", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReduceMixin": {".class": "SymbolTableNode", "cross_ref": "einops.layers.ReduceMixin", "kind": "Gdef"}, "_EinmixMixin": {".class": "SymbolTableNode", "cross_ref": "einops.layers._einmix._EinmixMixin", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.layers.tensorflow.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__author__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "einops.layers.tensorflow.__author__", "name": "__author__", "type": "builtins.str"}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.layers.tensorflow.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.layers.tensorflow.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.layers.tensorflow.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.layers.tensorflow.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.layers.tensorflow.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "tf": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "einops.layers.tensorflow.tf", "name": "tf", "type": {".class": "AnyType", "missing_import_name": "einops.layers.tensorflow.tf", "source_any": null, "type_of_any": 3}}}}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\einops\\layers\\tensorflow.py"}