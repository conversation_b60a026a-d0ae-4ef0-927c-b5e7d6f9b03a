{".class": "MypyFile", "_fullname": "einops.layers.keras", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "EinMix": {".class": "SymbolTableNode", "cross_ref": "einops.layers.tensorflow.EinMix", "kind": "Gdef"}, "Rearrange": {".class": "SymbolTableNode", "cross_ref": "einops.layers.tensorflow.Rearrange", "kind": "Gdef"}, "Reduce": {".class": "SymbolTableNode", "cross_ref": "einops.layers.tensorflow.Reduce", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.layers.keras.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__author__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "einops.layers.keras.__author__", "name": "__author__", "type": "builtins.str"}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.layers.keras.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.layers.keras.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.layers.keras.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.layers.keras.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.layers.keras.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "keras_custom_objects": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "einops.layers.keras.keras_custom_objects", "name": "keras_custom_objects", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}, "builtins.type"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\einops\\layers\\keras.py"}