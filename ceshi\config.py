import torch


class config:
    # >>>>>>> 关键路径配置 <<<<<<<<<
    data_root = "./data/SEED_DE_62_5_LOO/"  # 数据根目录（包含 session_1/2/3）
    # 新增关于数据结构的配置 [!新增!]
    session_pattern = "session_{}"  # 会话目录命名模式（例如: session_1）
    subject_pattern = "sub_{}"  # 受试者目录命名模式（例如: sub_1）
    n_subjects = 15  # 受试者总数
    n_sessions = 3  # 每个受试者的会话数
    input_dim = 310

    # >>>>>>> 训练参数 <<<<<<<<<
    epochs_per_subject = 30  # 每个受试者的训练轮次
    batch_size = 512
    lr = 1e-4
    lr_decay = 0.95
    min_lr = 1e-6
    

    # >>>>>>> 持续学习缓冲 <<<<<<<<<
    replay_buffer_size = 2048  # 扩大缓冲容量
    store_prob = 0.35  # 提高采样率保留更多关键样本
    # ==== 新增遗忘相关配置 ====
    ul_epochs = 30  # [!新增此行!] 控制遗忘训练的微调轮次
    
    config.update({
        'distill_weight': 0.5,  # 蒸馏损失权重
        'teacher_momentum': 0.999  # 教师模型动量系数
    })
    # >>>>>>> 设备与日志 <<<<<<<<<
    device = "cuda" if torch.cuda.is_available() else "cpu"


config = config()
