{".class": "MypyFile", "_fullname": "textwrap", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TextWrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "textwrap.TextWrapper", "name": "TextWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "textwrap.TextWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "textwrap", "mro": ["textwrap.TextWrapper", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, 5], "arg_names": ["self", "width", "initial_indent", "subsequent_indent", "expand_tabs", "replace_whitespace", "fix_sentence_endings", "break_long_words", "drop_whitespace", "break_on_hyphens", "tabsize", "max_lines", "placeholder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "textwrap.TextWrapper.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, 5], "arg_names": ["self", "width", "initial_indent", "subsequent_indent", "expand_tabs", "replace_whitespace", "fix_sentence_endings", "break_long_words", "drop_whitespace", "break_on_hyphens", "tabsize", "max_lines", "placeholder"], "arg_types": ["textwrap.TextWrapper", "builtins.int", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TextWrapper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fix_sentence_endings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chunks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "textwrap.TextWrapper._fix_sentence_endings", "name": "_fix_sentence_endings", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chunks"], "arg_types": ["textwrap.TextWrapper", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fix_sentence_endings of TextWrapper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_long_word": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "reversed_chunks", "cur_line", "cur_len", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "textwrap.TextWrapper._handle_long_word", "name": "_handle_long_word", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "reversed_chunks", "cur_line", "cur_len", "width"], "arg_types": ["textwrap.TextWrapper", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_long_word of TextWrapper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_munge_whitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "textwrap.TextWrapper._munge_whitespace", "name": "_munge_whitespace", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["textwrap.TextWrapper", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_munge_whitespace of TextWrapper", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_split": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "textwrap.TextWrapper._split", "name": "_split", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["textwrap.TextWrapper", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_split of TextWrapper", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_split_chunks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "textwrap.TextWrapper._split_chunks", "name": "_split_chunks", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["textwrap.TextWrapper", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_split_chunks of TextWrapper", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_wrap_chunks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chunks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "textwrap.TextWrapper._wrap_chunks", "name": "_wrap_chunks", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chunks"], "arg_types": ["textwrap.TextWrapper", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_wrap_chunks of TextWrapper", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "break_long_words": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "textwrap.TextWrapper.break_long_words", "name": "break_long_words", "type": "builtins.bool"}}, "break_on_hyphens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "textwrap.TextWrapper.break_on_hyphens", "name": "break_on_hyphens", "type": "builtins.bool"}}, "drop_whitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "textwrap.TextWrapper.drop_whitespace", "name": "drop_whitespace", "type": "builtins.bool"}}, "expand_tabs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "textwrap.TextWrapper.expand_tabs", "name": "expand_tabs", "type": "builtins.bool"}}, "fill": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "textwrap.TextWrapper.fill", "name": "fill", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["textwrap.TextWrapper", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fill of TextWrapper", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fix_sentence_endings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "textwrap.TextWrapper.fix_sentence_endings", "name": "fix_sentence_endings", "type": "builtins.bool"}}, "initial_indent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "textwrap.TextWrapper.initial_indent", "name": "initial_indent", "type": "builtins.str"}}, "max_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "textwrap.TextWrapper.max_lines", "name": "max_lines", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "placeholder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "textwrap.TextWrapper.placeholder", "name": "placeholder", "type": "builtins.str"}}, "replace_whitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "textwrap.TextWrapper.replace_whitespace", "name": "replace_whitespace", "type": "builtins.bool"}}, "sentence_end_re": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "textwrap.TextWrapper.sentence_end_re", "name": "sentence_end_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "subsequent_indent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "textwrap.TextWrapper.subsequent_indent", "name": "subsequent_indent", "type": "builtins.str"}}, "tabsize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "textwrap.TextWrapper.tabsize", "name": "tabsize", "type": "builtins.int"}}, "unicode_whitespace_trans": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "textwrap.TextWrapper.unicode_whitespace_trans", "name": "unicode_whitespace_trans", "type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "uspace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "textwrap.TextWrapper.uspace", "name": "uspace", "type": "builtins.int"}}, "whitespace_trans": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "textwrap.TextWrapper.whitespace_trans", "name": "whitespace_trans", "type": "builtins.str"}}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "textwrap.TextWrapper.width", "name": "width", "type": "builtins.int"}}, "wordsep_re": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "textwrap.TextWrapper.wordsep_re", "name": "wordsep_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "wordsep_simple_re": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "textwrap.TextWrapper.wordsep_simple_re", "name": "wordsep_simple_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "wrap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "textwrap.TextWrapper.wrap", "name": "wrap", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["textwrap.TextWrapper", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrap of TextWrapper", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "x": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "textwrap.TextWrapper.x", "name": "x", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "textwrap.TextWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "textwrap.TextWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "textwrap.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "textwrap.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "textwrap.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "textwrap.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "textwrap.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "textwrap.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "textwrap.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "dedent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "textwrap.dedent", "name": "dedent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dedent", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fill": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["text", "width", "initial_indent", "subsequent_indent", "expand_tabs", "tabsize", "replace_whitespace", "fix_sentence_endings", "break_long_words", "break_on_hyphens", "drop_whitespace", "max_lines", "placeholder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "textwrap.fill", "name": "fill", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["text", "width", "initial_indent", "subsequent_indent", "expand_tabs", "tabsize", "replace_whitespace", "fix_sentence_endings", "break_long_words", "break_on_hyphens", "drop_whitespace", "max_lines", "placeholder"], "arg_types": ["builtins.str", "builtins.int", "builtins.str", "builtins.str", "builtins.bool", "builtins.int", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fill", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "indent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["text", "prefix", "predicate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "textwrap.indent", "name": "indent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["text", "prefix", "predicate"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "indent", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shorten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["text", "width", "initial_indent", "subsequent_indent", "expand_tabs", "tabsize", "replace_whitespace", "fix_sentence_endings", "break_long_words", "break_on_hyphens", "drop_whitespace", "placeholder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "textwrap.shorten", "name": "shorten", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["text", "width", "initial_indent", "subsequent_indent", "expand_tabs", "tabsize", "replace_whitespace", "fix_sentence_endings", "break_long_words", "break_on_hyphens", "drop_whitespace", "placeholder"], "arg_types": ["builtins.str", "builtins.int", "builtins.str", "builtins.str", "builtins.bool", "builtins.int", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shorten", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "wrap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["text", "width", "initial_indent", "subsequent_indent", "expand_tabs", "tabsize", "replace_whitespace", "fix_sentence_endings", "break_long_words", "break_on_hyphens", "drop_whitespace", "max_lines", "placeholder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "textwrap.wrap", "name": "wrap", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["text", "width", "initial_indent", "subsequent_indent", "expand_tabs", "tabsize", "replace_whitespace", "fix_sentence_endings", "break_long_words", "break_on_hyphens", "drop_whitespace", "max_lines", "placeholder"], "arg_types": ["builtins.str", "builtins.int", "builtins.str", "builtins.str", "builtins.bool", "builtins.int", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrap", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\textwrap.pyi"}