{".class": "MypyFile", "_fullname": "jinja2.optimizer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Environment": {".class": "SymbolTableNode", "cross_ref": "jinja2.environment.Environment", "kind": "Gdef"}, "NodeTransformer": {".class": "SymbolTableNode", "cross_ref": "jinja2.visitor.NodeTransformer", "kind": "Gdef"}, "Optimizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.visitor.NodeTransformer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.optimizer.Optimizer", "name": "Optimizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.optimizer.Optimizer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jinja2.optimizer", "mro": ["jinja2.optimizer.Optimizer", "jinja2.visitor.NodeTransformer", "jinja2.visitor.NodeVisitor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "environment"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.optimizer.Optimizer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "environment"], "arg_types": ["jinja2.optimizer.Optimizer", {".class": "UnionType", "items": ["jinja2.environment.Environment", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Optimizer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "environment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.optimizer.Optimizer.environment", "name": "environment", "type": {".class": "UnionType", "items": ["jinja2.environment.Environment", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "generic_visit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "node", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.optimizer.Optimizer.generic_visit", "name": "generic_visit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "node", "args", "kwargs"], "arg_types": ["jinja2.optimizer.Optimizer", "jinja2.nodes.Node", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generic_visit of Optimizer", "ret_type": "jinja2.nodes.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.optimizer.Optimizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.optimizer.Optimizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.optimizer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.optimizer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.optimizer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.optimizer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.optimizer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.optimizer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "nodes": {".class": "SymbolTableNode", "cross_ref": "jinja2.nodes", "kind": "Gdef"}, "optimize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["node", "environment"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.optimizer.optimize", "name": "optimize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["node", "environment"], "arg_types": ["jinja2.nodes.Node", "jinja2.environment.Environment"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "optimize", "ret_type": "jinja2.nodes.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\jinja2\\optimizer.py"}