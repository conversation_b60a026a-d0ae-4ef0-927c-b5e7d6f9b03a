{"data_mtime": 1754286351, "dep_lines": [5, 8, 9, 12, 3, 4, 5, 6, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 25, 10, 10, 20, 5, 5, 30, 30, 30], "dependencies": ["collections.abc", "jinja2.runtime", "jinja2.utils", "jinja2.environment", "operator", "typing", "collections", "numbers", "builtins", "_frozen_importlib", "_operator", "abc"], "hash": "cc9cfd9778fb9101439d34a14a233461654ba864", "id": "jinja2.tests", "ignore_all": true, "interface_hash": "7b4219d6757663ab0504a910f63e283e37803987", "mtime": 1716993775, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\jinja2\\tests.py", "plugin_data": null, "size": 5926, "suppressed": [], "version_id": "1.15.0"}