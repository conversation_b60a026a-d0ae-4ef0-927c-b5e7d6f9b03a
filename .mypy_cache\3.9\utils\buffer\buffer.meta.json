{"data_mtime": 1754286791, "dep_lines": [7, 1, 4, 6, 59, 5, 59, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 20, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["utils.buffer.buffer_utils", "utils.setup_elements", "utils.utils", "torch.nn", "utils.name_match", "torch", "utils", "builtins", "_frozen_importlib", "_typeshed", "abc", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.module", "torch.types", "typing"], "hash": "01fdcdf67ecc7b1fd232caa698c28c2cb644ec23", "id": "utils.buffer.buffer", "ignore_all": true, "interface_hash": "80f563468304492a715fe1d728e9e66830079cbc", "mtime": 1753084547, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\utils\\buffer\\buffer.py", "plugin_data": null, "size": 4597, "suppressed": [], "version_id": "1.15.0"}