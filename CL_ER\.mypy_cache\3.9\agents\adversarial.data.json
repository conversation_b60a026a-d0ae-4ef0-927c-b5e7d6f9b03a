{".class": "MypyFile", "_fullname": "agents.adversarial", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BasicUnlearningAgent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["agents.exp_replay.ExperienceReplay"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "agents.adversarial.BasicUnlearningAgent", "name": "BasicUnlearningAgent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "agents.adversarial.BasicUnlearningAgent", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "agents.adversarial", "mro": ["agents.adversarial.BasicUnlearningAgent", "agents.exp_replay.ExperienceReplay", "agents.base.ContinualLearner", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "optimizer", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.adversarial.BasicUnlearningAgent.__init__", "name": "__init__", "type": null}}, "_basic_unlearning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "forget_x", "forget_y", "retain_x", "retain_y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.adversarial.BasicUnlearningAgent._basic_unlearning", "name": "_basic_unlearning", "type": null}}, "_get_task_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.adversarial.BasicUnlearningAgent._get_task_data", "name": "_get_task_data", "type": null}}, "_remove_task_from_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.adversarial.BasicUnlearningAgent._remove_task_from_buffer", "name": "_remove_task_from_buffer", "type": null}}, "evaluate_accuracy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.adversarial.BasicUnlearningAgent.evaluate_accuracy", "name": "evaluate_accuracy", "type": null}}, "evaluate_membership_inference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "forget_x", "forget_y", "retain_x", "retain_y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.adversarial.BasicUnlearningAgent.evaluate_membership_inference", "name": "evaluate_membership_inference", "type": null}}, "forget": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "forget_task_id", "subjects_to_keep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.adversarial.BasicUnlearningAgent.forget", "name": "forget", "type": null}}, "model_cls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.adversarial.BasicUnlearningAgent.model_cls", "name": "model_cls", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "model_encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.adversarial.BasicUnlearningAgent.model_encoder", "name": "model_encoder", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "unlearning_epochs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.adversarial.BasicUnlearningAgent.unlearning_epochs", "name": "unlearning_epochs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "agents.adversarial.BasicUnlearningAgent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "agents.adversarial.BasicUnlearningAgent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExperienceReplay": {".class": "SymbolTableNode", "cross_ref": "agents.exp_replay.ExperienceReplay", "kind": "Gdef"}, "F": {".class": "SymbolTableNode", "cross_ref": "torch.nn.functional", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.adversarial.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.adversarial.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.adversarial.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.adversarial.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.adversarial.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.adversarial.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "D:\\continual_learning\\CL_ER\\agents\\adversarial.py"}