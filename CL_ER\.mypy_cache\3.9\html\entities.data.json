{".class": "MypyFile", "_fullname": "html.entities", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "html.entities.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "html.entities.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "html.entities.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "html.entities.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "html.entities.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "html.entities.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "html.entities.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "codepoint2name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "html.entities.codepoint2name", "name": "codepoint2name", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "entitydefs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "html.entities.entitydefs", "name": "entitydefs", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "html5": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "html.entities.html5", "name": "html5", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "name2codepoint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "html.entities.name2codepoint", "name": "name2codepoint", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "path": "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\html\\entities.pyi"}