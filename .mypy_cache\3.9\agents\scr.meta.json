{"data_mtime": 1754286791, "dep_lines": [2, 3, 2, 4, 5, 6, 7, 8, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 5, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.utils.data", "utils.buffer.buffer", "torch.utils", "agents.base", "continuum.data_utils", "utils.setup_elements", "utils.utils", "kornia.augmentation", "torch.nn", "torch", "builtins", "_frozen_importlib", "_typeshed", "abc", "collections", "continuum", "enum", "kornia", "kornia.augmentation._2d", "kornia.augmentation._2d.base", "kornia.augmentation._2d.geometric", "kornia.augmentation._2d.geometric.base", "kornia.augmentation._2d.geometric.horizontal_flip", "kornia.augmentation._2d.geometric.resized_crop", "kornia.augmentation._2d.intensity", "kornia.augmentation._2d.intensity.base", "kornia.augmentation._2d.intensity.color_jitter", "kornia.augmentation._2d.intensity.grayscale", "kornia.augmentation.base", "kornia.constants", "kornia.core", "kornia.core.mixin", "kornia.core.mixin.onnx", "kornia.core.module", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "torch.utils.data.dataloader", "torch.utils.data.dataset", "torch.utils.data.sampler", "typing", "utils", "utils.buffer"], "hash": "ef2b038e743f3d90b3eb37602b279af21adc6fb4", "id": "agents.scr", "ignore_all": true, "interface_hash": "65d08e45601ca10a98c2c9ba2c2da936dc2ddb30", "mtime": 1751894900, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\agents\\scr.py", "plugin_data": null, "size": 3326, "suppressed": [], "version_id": "1.15.0"}