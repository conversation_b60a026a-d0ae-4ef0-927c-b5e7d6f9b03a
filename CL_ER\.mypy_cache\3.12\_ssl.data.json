{".class": "MypyFile", "_fullname": "_ssl", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ALERT_DESCRIPTION_ACCESS_DENIED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_ACCESS_DENIED", "name": "ALERT_DESCRIPTION_ACCESS_DENIED", "type": "builtins.int"}}, "ALERT_DESCRIPTION_BAD_CERTIFICATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_BAD_CERTIFICATE", "name": "ALERT_DESCRIPTION_BAD_CERTIFICATE", "type": "builtins.int"}}, "ALERT_DESCRIPTION_BAD_CERTIFICATE_HASH_VALUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_BAD_CERTIFICATE_HASH_VALUE", "name": "ALERT_DESCRIPTION_BAD_CERTIFICATE_HASH_VALUE", "type": "builtins.int"}}, "ALERT_DESCRIPTION_BAD_CERTIFICATE_STATUS_RESPONSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_BAD_CERTIFICATE_STATUS_RESPONSE", "name": "ALERT_DESCRIPTION_BAD_CERTIFICATE_STATUS_RESPONSE", "type": "builtins.int"}}, "ALERT_DESCRIPTION_BAD_RECORD_MAC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_BAD_RECORD_MAC", "name": "ALERT_DESCRIPTION_BAD_RECORD_MAC", "type": "builtins.int"}}, "ALERT_DESCRIPTION_CERTIFICATE_EXPIRED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_CERTIFICATE_EXPIRED", "name": "ALERT_DESCRIPTION_CERTIFICATE_EXPIRED", "type": "builtins.int"}}, "ALERT_DESCRIPTION_CERTIFICATE_REVOKED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_CERTIFICATE_REVOKED", "name": "ALERT_DESCRIPTION_CERTIFICATE_REVOKED", "type": "builtins.int"}}, "ALERT_DESCRIPTION_CERTIFICATE_UNKNOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_CERTIFICATE_UNKNOWN", "name": "ALERT_DESCRIPTION_CERTIFICATE_UNKNOWN", "type": "builtins.int"}}, "ALERT_DESCRIPTION_CERTIFICATE_UNOBTAINABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_CERTIFICATE_UNOBTAINABLE", "name": "ALERT_DESCRIPTION_CERTIFICATE_UNOBTAINABLE", "type": "builtins.int"}}, "ALERT_DESCRIPTION_CLOSE_NOTIFY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_CLOSE_NOTIFY", "name": "ALERT_DESCRIPTION_CLOSE_NOTIFY", "type": "builtins.int"}}, "ALERT_DESCRIPTION_DECODE_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_DECODE_ERROR", "name": "ALERT_DESCRIPTION_DECODE_ERROR", "type": "builtins.int"}}, "ALERT_DESCRIPTION_DECOMPRESSION_FAILURE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_DECOMPRESSION_FAILURE", "name": "ALERT_DESCRIPTION_DECOMPRESSION_FAILURE", "type": "builtins.int"}}, "ALERT_DESCRIPTION_DECRYPT_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_DECRYPT_ERROR", "name": "ALERT_DESCRIPTION_DECRYPT_ERROR", "type": "builtins.int"}}, "ALERT_DESCRIPTION_HANDSHAKE_FAILURE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_HANDSHAKE_FAILURE", "name": "ALERT_DESCRIPTION_HANDSHAKE_FAILURE", "type": "builtins.int"}}, "ALERT_DESCRIPTION_ILLEGAL_PARAMETER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_ILLEGAL_PARAMETER", "name": "ALERT_DESCRIPTION_ILLEGAL_PARAMETER", "type": "builtins.int"}}, "ALERT_DESCRIPTION_INSUFFICIENT_SECURITY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_INSUFFICIENT_SECURITY", "name": "ALERT_DESCRIPTION_INSUFFICIENT_SECURITY", "type": "builtins.int"}}, "ALERT_DESCRIPTION_INTERNAL_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_INTERNAL_ERROR", "name": "ALERT_DESCRIPTION_INTERNAL_ERROR", "type": "builtins.int"}}, "ALERT_DESCRIPTION_NO_RENEGOTIATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_NO_RENEGOTIATION", "name": "ALERT_DESCRIPTION_NO_RENEGOTIATION", "type": "builtins.int"}}, "ALERT_DESCRIPTION_PROTOCOL_VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_PROTOCOL_VERSION", "name": "ALERT_DESCRIPTION_PROTOCOL_VERSION", "type": "builtins.int"}}, "ALERT_DESCRIPTION_RECORD_OVERFLOW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_RECORD_OVERFLOW", "name": "ALERT_DESCRIPTION_RECORD_OVERFLOW", "type": "builtins.int"}}, "ALERT_DESCRIPTION_UNEXPECTED_MESSAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_UNEXPECTED_MESSAGE", "name": "ALERT_DESCRIPTION_UNEXPECTED_MESSAGE", "type": "builtins.int"}}, "ALERT_DESCRIPTION_UNKNOWN_CA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_UNKNOWN_CA", "name": "ALERT_DESCRIPTION_UNKNOWN_CA", "type": "builtins.int"}}, "ALERT_DESCRIPTION_UNKNOWN_PSK_IDENTITY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_UNKNOWN_PSK_IDENTITY", "name": "ALERT_DESCRIPTION_UNKNOWN_PSK_IDENTITY", "type": "builtins.int"}}, "ALERT_DESCRIPTION_UNRECOGNIZED_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_UNRECOGNIZED_NAME", "name": "ALERT_DESCRIPTION_UNRECOGNIZED_NAME", "type": "builtins.int"}}, "ALERT_DESCRIPTION_UNSUPPORTED_CERTIFICATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_UNSUPPORTED_CERTIFICATE", "name": "ALERT_DESCRIPTION_UNSUPPORTED_CERTIFICATE", "type": "builtins.int"}}, "ALERT_DESCRIPTION_UNSUPPORTED_EXTENSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_UNSUPPORTED_EXTENSION", "name": "ALERT_DESCRIPTION_UNSUPPORTED_EXTENSION", "type": "builtins.int"}}, "ALERT_DESCRIPTION_USER_CANCELLED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ALERT_DESCRIPTION_USER_CANCELLED", "name": "ALERT_DESCRIPTION_USER_CANCELLED", "type": "builtins.int"}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CERT_NONE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.CERT_NONE", "name": "CERT_NONE", "type": "builtins.int"}}, "CERT_OPTIONAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.CERT_OPTIONAL", "name": "CERT_OPTIONAL", "type": "builtins.int"}}, "CERT_REQUIRED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.CERT_REQUIRED", "name": "CERT_REQUIRED", "type": "builtins.int"}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Certificate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ssl.Certificate", "name": "Certificate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_ssl.Certificate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ssl", "mro": ["_ssl.Certificate", "builtins.object"], "names": {".class": "SymbolTable", "get_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl.Certificate.get_info", "name": "get_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_ssl.Certificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_info of Certificate", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_ssl._CertInfo"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "public_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_ssl.Certificate.public_bytes", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ssl.Certificate.public_bytes", "name": "public_bytes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_ssl.Certificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_bytes of Certificate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ssl.Certificate.public_bytes", "name": "public_bytes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_ssl.Certificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_bytes of Certificate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ssl.Certificate.public_bytes", "name": "public_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": ["_ssl.Certificate", {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_bytes of Certificate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ssl.Certificate.public_bytes", "name": "public_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": ["_ssl.Certificate", {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_bytes of Certificate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ssl.Certificate.public_bytes", "name": "public_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_ssl.Certificate", {".class": "LiteralType", "fallback": "builtins.int", "value": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_bytes of Certificate", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ssl.Certificate.public_bytes", "name": "public_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_ssl.Certificate", {".class": "LiteralType", "fallback": "builtins.int", "value": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_bytes of Certificate", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ssl.Certificate.public_bytes", "name": "public_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_ssl.Certificate", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_bytes of Certificate", "ret_type": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ssl.Certificate.public_bytes", "name": "public_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_ssl.Certificate", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_bytes of Certificate", "ret_type": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_ssl.Certificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_bytes of Certificate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": ["_ssl.Certificate", {".class": "LiteralType", "fallback": "builtins.int", "value": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_bytes of Certificate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_ssl.Certificate", {".class": "LiteralType", "fallback": "builtins.int", "value": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_bytes of Certificate", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_ssl.Certificate", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_bytes of Certificate", "ret_type": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_ssl.Certificate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_ssl.Certificate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ENCODING_DER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ENCODING_DER", "name": "ENCODING_DER", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}}}, "ENCODING_PEM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.ENCODING_PEM", "name": "ENCODING_PEM", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}}}, "HAS_ALPN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.HAS_ALPN", "name": "HAS_ALPN", "type": "builtins.bool"}}, "HAS_ECDH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.HAS_ECDH", "name": "HAS_ECDH", "type": "builtins.bool"}}, "HAS_NPN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.HAS_NPN", "name": "HAS_NPN", "type": "builtins.bool"}}, "HAS_SNI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.HAS_SNI", "name": "HAS_SNI", "type": "builtins.bool"}}, "HAS_SSLv2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.HAS_SSLv2", "name": "HAS_SSLv2", "type": "builtins.bool"}}, "HAS_SSLv3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.HAS_SSLv3", "name": "HAS_SSLv3", "type": "builtins.bool"}}, "HAS_TLS_UNIQUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.HAS_TLS_UNIQUE", "name": "HAS_TLS_UNIQUE", "type": "builtins.bool"}}, "HAS_TLSv1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.HAS_TLSv1", "name": "HAS_TLSv1", "type": "builtins.bool"}}, "HAS_TLSv1_1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.HAS_TLSv1_1", "name": "HAS_TLSv1_1", "type": "builtins.bool"}}, "HAS_TLSv1_2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.HAS_TLSv1_2", "name": "HAS_TLSv1_2", "type": "builtins.bool"}}, "HAS_TLSv1_3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.HAS_TLSv1_3", "name": "HAS_TLSv1_3", "type": "builtins.bool"}}, "HOSTFLAG_ALWAYS_CHECK_SUBJECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.HOSTFLAG_ALWAYS_CHECK_SUBJECT", "name": "HOSTFLAG_ALWAYS_CHECK_SUBJECT", "type": "builtins.int"}}, "HOSTFLAG_MULTI_LABEL_WILDCARDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.HOSTFLAG_MULTI_LABEL_WILDCARDS", "name": "HOSTFLAG_MULTI_LABEL_WILDCARDS", "type": "builtins.int"}}, "HOSTFLAG_NEVER_CHECK_SUBJECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.HOSTFLAG_NEVER_CHECK_SUBJECT", "name": "HOSTFLAG_NEVER_CHECK_SUBJECT", "type": "builtins.int"}}, "HOSTFLAG_NO_PARTIAL_WILDCARDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.HOSTFLAG_NO_PARTIAL_WILDCARDS", "name": "HOSTFLAG_NO_PARTIAL_WILDCARDS", "type": "builtins.int"}}, "HOSTFLAG_NO_WILDCARDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.HOSTFLAG_NO_WILDCARDS", "name": "HOSTFLAG_NO_WILDCARDS", "type": "builtins.int"}}, "HOSTFLAG_SINGLE_LABEL_SUBDOMAINS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.HOSTFLAG_SINGLE_LABEL_SUBDOMAINS", "name": "HOSTFLAG_SINGLE_LABEL_SUBDOMAINS", "type": "builtins.int"}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MemoryBIO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ssl.MemoryBIO", "name": "MemoryBIO", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_ssl.MemoryBIO", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ssl", "mro": ["_ssl.MemoryBIO", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "_ssl.MemoryBIO.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_ssl.MemoryBIO.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_ssl.MemoryBIO", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of MemoryBIO", "ret_type": "_ssl.MemoryBIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_ssl.MemoryBIO.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_ssl.MemoryBIO", "values": [], "variance": 0}]}}}, "eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ssl.MemoryBIO.eof", "name": "eof", "type": "builtins.bool"}}, "pending": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ssl.MemoryBIO.pending", "name": "pending", "type": "builtins.int"}}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl.MemoryBIO.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": ["_ssl.MemoryBIO", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read of MemoryBIO", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl.MemoryBIO.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_ssl.MemoryBIO", "_collections_abc.<PERSON><PERSON>er"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of MemoryBIO", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl.MemoryBIO.write_eof", "name": "write_eof", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_ssl.MemoryBIO"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_eof of MemoryBIO", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_ssl.MemoryBIO.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_ssl.MemoryBIO", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotRequired": {".class": "SymbolTableNode", "cross_ref": "typing.NotRequired", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OPENSSL_VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OPENSSL_VERSION", "name": "OPENSSL_VERSION", "type": "builtins.str"}}, "OPENSSL_VERSION_INFO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OPENSSL_VERSION_INFO", "name": "OPENSSL_VERSION_INFO", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "OPENSSL_VERSION_NUMBER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OPENSSL_VERSION_NUMBER", "name": "OPENSSL_VERSION_NUMBER", "type": "builtins.int"}}, "OP_ALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OP_ALL", "name": "OP_ALL", "type": "builtins.int"}}, "OP_CIPHER_SERVER_PREFERENCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OP_CIPHER_SERVER_PREFERENCE", "name": "OP_CIPHER_SERVER_PREFERENCE", "type": "builtins.int"}}, "OP_ENABLE_KTLS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OP_ENABLE_KTLS", "name": "OP_ENABLE_KTLS", "type": "builtins.int"}}, "OP_ENABLE_MIDDLEBOX_COMPAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OP_ENABLE_MIDDLEBOX_COMPAT", "name": "OP_ENABLE_MIDDLEBOX_COMPAT", "type": "builtins.int"}}, "OP_IGNORE_UNEXPECTED_EOF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OP_IGNORE_UNEXPECTED_EOF", "name": "OP_IGNORE_UNEXPECTED_EOF", "type": "builtins.int"}}, "OP_LEGACY_SERVER_CONNECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OP_LEGACY_SERVER_CONNECT", "name": "OP_LEGACY_SERVER_CONNECT", "type": "builtins.int"}}, "OP_NO_COMPRESSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OP_NO_COMPRESSION", "name": "OP_NO_COMPRESSION", "type": "builtins.int"}}, "OP_NO_RENEGOTIATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OP_NO_RENEGOTIATION", "name": "OP_NO_RENEGOTIATION", "type": "builtins.int"}}, "OP_NO_SSLv2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OP_NO_SSLv2", "name": "OP_NO_SSLv2", "type": "builtins.int"}}, "OP_NO_SSLv3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OP_NO_SSLv3", "name": "OP_NO_SSLv3", "type": "builtins.int"}}, "OP_NO_TICKET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OP_NO_TICKET", "name": "OP_NO_TICKET", "type": "builtins.int"}}, "OP_NO_TLSv1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OP_NO_TLSv1", "name": "OP_NO_TLSv1", "type": "builtins.int"}}, "OP_NO_TLSv1_1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OP_NO_TLSv1_1", "name": "OP_NO_TLSv1_1", "type": "builtins.int"}}, "OP_NO_TLSv1_2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OP_NO_TLSv1_2", "name": "OP_NO_TLSv1_2", "type": "builtins.int"}}, "OP_NO_TLSv1_3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OP_NO_TLSv1_3", "name": "OP_NO_TLSv1_3", "type": "builtins.int"}}, "OP_SINGLE_DH_USE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OP_SINGLE_DH_USE", "name": "OP_SINGLE_DH_USE", "type": "builtins.int"}}, "OP_SINGLE_ECDH_USE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.OP_SINGLE_ECDH_USE", "name": "OP_SINGLE_ECDH_USE", "type": "builtins.int"}}, "PROTOCOL_SSLv23": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.PROTOCOL_SSLv23", "name": "PROTOCOL_SSLv23", "type": "builtins.int"}}, "PROTOCOL_TLS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.PROTOCOL_TLS", "name": "PROTOCOL_TLS", "type": "builtins.int"}}, "PROTOCOL_TLS_CLIENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.PROTOCOL_TLS_CLIENT", "name": "PROTOCOL_TLS_CLIENT", "type": "builtins.int"}}, "PROTOCOL_TLS_SERVER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.PROTOCOL_TLS_SERVER", "name": "PROTOCOL_TLS_SERVER", "type": "builtins.int"}}, "PROTOCOL_TLSv1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.PROTOCOL_TLSv1", "name": "PROTOCOL_TLSv1", "type": "builtins.int"}}, "PROTOCOL_TLSv1_1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.PROTOCOL_TLSv1_1", "name": "PROTOCOL_TLSv1_1", "type": "builtins.int"}}, "PROTOCOL_TLSv1_2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.PROTOCOL_TLSv1_2", "name": "PROTOCOL_TLSv1_2", "type": "builtins.int"}}, "PROTO_MAXIMUM_SUPPORTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.PROTO_MAXIMUM_SUPPORTED", "name": "PROTO_MAXIMUM_SUPPORTED", "type": "builtins.int"}}, "PROTO_MINIMUM_SUPPORTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.PROTO_MINIMUM_SUPPORTED", "name": "PROTO_MINIMUM_SUPPORTED", "type": "builtins.int"}}, "PROTO_SSLv3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.PROTO_SSLv3", "name": "PROTO_SSLv3", "type": "builtins.int"}}, "PROTO_TLSv1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.PROTO_TLSv1", "name": "PROTO_TLSv1", "type": "builtins.int"}}, "PROTO_TLSv1_1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.PROTO_TLSv1_1", "name": "PROTO_TLSv1_1", "type": "builtins.int"}}, "PROTO_TLSv1_2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.PROTO_TLSv1_2", "name": "PROTO_TLSv1_2", "type": "builtins.int"}}, "PROTO_TLSv1_3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.PROTO_TLSv1_3", "name": "PROTO_TLSv1_3", "type": "builtins.int"}}, "RAND_add": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl.RAND_add", "name": "RAND_add", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "_collections_abc.<PERSON><PERSON>er"], "uses_pep604_syntax": true}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RAND_add", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "RAND_bytes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl.RAND_bytes", "name": "RAND_bytes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RAND_bytes", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "RAND_status": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl.RAND_status", "name": "RAND_status", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "RAND_status", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SSLCertVerificationError": {".class": "SymbolTableNode", "cross_ref": "ssl.SSLCertVerificationError", "kind": "Gdef"}, "SSLContext": {".class": "SymbolTableNode", "cross_ref": "ssl.SSLContext", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SSLEOFError": {".class": "SymbolTableNode", "cross_ref": "ssl.SSLEOFError", "kind": "Gdef"}, "SSLError": {".class": "SymbolTableNode", "cross_ref": "ssl.SSLError", "kind": "Gdef"}, "SSLObject": {".class": "SymbolTableNode", "cross_ref": "ssl.SSLObject", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SSLSession": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ssl.SSLSession", "name": "SSLSession", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_ssl.SSLSession", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ssl", "mro": ["_ssl.SSLSession", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "_ssl.SSLSession.__hash__", "name": "__hash__", "type": {".class": "NoneType"}}}, "has_ticket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_ssl.SSLSession.has_ticket", "name": "has_ticket", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_ssl.SSLSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_ticket of SSLSession", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_ssl.SSLSession.has_ticket", "name": "has_ticket", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_ssl.SSLSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_ticket of SSLSession", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_ssl.SSLSession.id", "name": "id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_ssl.SSLSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "id of SSLSession", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_ssl.SSLSession.id", "name": "id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_ssl.SSLSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "id of SSLSession", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ticket_lifetime_hint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_ssl.SSLSession.ticket_lifetime_hint", "name": "ticket_lifetime_hint", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_ssl.SSLSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ticket_lifetime_hint of SSLSession", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_ssl.SSLSession.ticket_lifetime_hint", "name": "ticket_lifetime_hint", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_ssl.SSLSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ticket_lifetime_hint of SSLSession", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_ssl.SSLSession.time", "name": "time", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_ssl.SSLSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "time of SSLSession", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_ssl.SSLSession.time", "name": "time", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_ssl.SSLSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "time of SSLSession", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_ssl.SSLSession.timeout", "name": "timeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_ssl.SSLSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timeout of SSLSession", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_ssl.SSLSession.timeout", "name": "timeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_ssl.SSLSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timeout of SSLSession", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_ssl.SSLSession.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_ssl.SSLSession", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSLSyscallError": {".class": "SymbolTableNode", "cross_ref": "ssl.SSLSyscallError", "kind": "Gdef"}, "SSLWantReadError": {".class": "SymbolTableNode", "cross_ref": "ssl.SSLWantReadError", "kind": "Gdef"}, "SSLWantWriteError": {".class": "SymbolTableNode", "cross_ref": "ssl.SSLWantWriteError", "kind": "Gdef"}, "SSLZeroReturnError": {".class": "SymbolTableNode", "cross_ref": "ssl.SSLZeroReturnError", "kind": "Gdef"}, "SSL_ERROR_EOF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.SSL_ERROR_EOF", "name": "SSL_ERROR_EOF", "type": "builtins.int"}}, "SSL_ERROR_INVALID_ERROR_CODE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.SSL_ERROR_INVALID_ERROR_CODE", "name": "SSL_ERROR_INVALID_ERROR_CODE", "type": "builtins.int"}}, "SSL_ERROR_SSL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.SSL_ERROR_SSL", "name": "SSL_ERROR_SSL", "type": "builtins.int"}}, "SSL_ERROR_SYSCALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.SSL_ERROR_SYSCALL", "name": "SSL_ERROR_SYSCALL", "type": "builtins.int"}}, "SSL_ERROR_WANT_CONNECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.SSL_ERROR_WANT_CONNECT", "name": "SSL_ERROR_WANT_CONNECT", "type": "builtins.int"}}, "SSL_ERROR_WANT_READ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.SSL_ERROR_WANT_READ", "name": "SSL_ERROR_WANT_READ", "type": "builtins.int"}}, "SSL_ERROR_WANT_WRITE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.SSL_ERROR_WANT_WRITE", "name": "SSL_ERROR_WANT_WRITE", "type": "builtins.int"}}, "SSL_ERROR_WANT_X509_LOOKUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.SSL_ERROR_WANT_X509_LOOKUP", "name": "SSL_ERROR_WANT_X509_LOOKUP", "type": "builtins.int"}}, "SSL_ERROR_ZERO_RETURN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.SSL_ERROR_ZERO_RETURN", "name": "SSL_ERROR_ZERO_RETURN", "type": "builtins.int"}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StrOrBytesPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.StrOrBytesPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "VERIFY_ALLOW_PROXY_CERTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.VERIFY_ALLOW_PROXY_CERTS", "name": "VERIFY_ALLOW_PROXY_CERTS", "type": "builtins.int"}}, "VERIFY_CRL_CHECK_CHAIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.VERIFY_CRL_CHECK_CHAIN", "name": "VERIFY_CRL_CHECK_CHAIN", "type": "builtins.int"}}, "VERIFY_CRL_CHECK_LEAF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.VERIFY_CRL_CHECK_LEAF", "name": "VERIFY_CRL_CHECK_LEAF", "type": "builtins.int"}}, "VERIFY_DEFAULT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.VERIFY_DEFAULT", "name": "VERIFY_DEFAULT", "type": "builtins.int"}}, "VERIFY_X509_PARTIAL_CHAIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.VERIFY_X509_PARTIAL_CHAIN", "name": "VERIFY_X509_PARTIAL_CHAIN", "type": "builtins.int"}}, "VERIFY_X509_STRICT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.VERIFY_X509_STRICT", "name": "VERIFY_X509_STRICT", "type": "builtins.int"}}, "VERIFY_X509_TRUSTED_FIRST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.VERIFY_X509_TRUSTED_FIRST", "name": "VERIFY_X509_TRUSTED_FIRST", "type": "builtins.int"}}, "_CertInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ssl._CertInfo", "name": "_CertInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_ssl._CertInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ssl", "mro": ["_ssl._CertInfo", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["subject", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], ["issuer", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], ["version", "builtins.int"], ["serialNumber", "builtins.str"], ["notBefore", "builtins.str"], ["notAfter", "builtins.str"], ["subjectAltName", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["OCSP", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["caIssuers", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["crlDistributionPoints", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": ["issuer", "notAfter", "notBefore", "serialNumber", "subject", "version"]}}}, "_Cipher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ssl._Cipher", "name": "_Cipher", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_ssl._Cipher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ssl", "mro": ["_ssl._Cipher", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["aead", "builtins.bool"], ["alg_bits", "builtins.int"], ["auth", "builtins.str"], ["description", "builtins.str"], ["digest", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["id", "builtins.int"], ["kea", "builtins.str"], ["name", "builtins.str"], ["protocol", "builtins.str"], ["strength_bits", "builtins.int"], ["symmetric", "builtins.str"]], "readonly_keys": [], "required_keys": ["aead", "alg_bits", "auth", "description", "digest", "id", "kea", "name", "protocol", "strength_bits", "symmetric"]}}}, "_DEFAULT_CIPHERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl._DEFAULT_CIPHERS", "name": "_DEFAULT_CIPHERS", "type": "builtins.str"}}, "_EnumRetType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "_ssl._EnumRetType", "line": 61, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "builtins.bool"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_OPENSSL_API_VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl._OPENSSL_API_VERSION", "name": "_OPENSSL_API_VERSION", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_PCTRTT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "_ssl._PCTRTT", "line": 19, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_PCTRTTT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "_ssl._PCTRTTT", "line": 20, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_ssl._PCTRTT"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_PasswordType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "_ssl._PasswordType", "line": 18, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.str", "builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": true}}}, "_PeerCertRetDictType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "_ssl._PeerCertRetDictType", "line": 21, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_ssl._PCTRTTT"}, {".class": "TypeAliasType", "args": [], "type_ref": "_ssl._PCTRTT"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_SSLContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_ssl._SSLContext", "name": "_SSLContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_ssl._SSLContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_ssl", "mro": ["_ssl._SSLContext", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "_ssl._SSLContext.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_ssl._SSLContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_ssl._SSLContext", "values": [], "variance": 0}}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _SSLContext", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_ssl._SSLContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_ssl._SSLContext", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_ssl._SSLContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_ssl._SSLContext", "values": [], "variance": 0}]}}}, "cert_store_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl._SSLContext.cert_store_stats", "name": "cert_store_stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_ssl._SSLContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cert_store_stats of _SSLContext", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_hostname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ssl._SSLContext.check_hostname", "name": "check_hostname", "type": "builtins.bool"}}, "get_ca_certs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_ssl._SSLContext.get_ca_certs", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ssl._SSLContext.get_ca_certs", "name": "get_ca_certs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["_ssl._SSLContext", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ca_certs of _SSLContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_ssl._PeerCertRetDictType"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ssl._SSLContext.get_ca_certs", "name": "get_ca_certs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["_ssl._SSLContext", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ca_certs of _SSLContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_ssl._PeerCertRetDictType"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ssl._SSLContext.get_ca_certs", "name": "get_ca_certs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["_ssl._SSLContext", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ca_certs of _SSLContext", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ssl._SSLContext.get_ca_certs", "name": "get_ca_certs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["_ssl._SSLContext", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ca_certs of _SSLContext", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_ssl._SSLContext.get_ca_certs", "name": "get_ca_certs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["_ssl._SSLContext", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ca_certs of _SSLContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_ssl._SSLContext.get_ca_certs", "name": "get_ca_certs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["_ssl._SSLContext", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ca_certs of _SSLContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["_ssl._SSLContext", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ca_certs of _SSLContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_ssl._PeerCertRetDictType"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["_ssl._SSLContext", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ca_certs of _SSLContext", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["_ssl._SSLContext", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ca_certs of _SSLContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "get_ciphers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl._SSLContext.get_ciphers", "name": "get_ciphers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_ssl._SSLContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ciphers of _SSLContext", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_ssl._Cipher"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "keylog_filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ssl._SSLContext.keylog_filename", "name": "keylog_filename", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "load_cert_chain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "certfile", "keyfile", "password"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl._SSLContext.load_cert_chain", "name": "load_cert_chain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "certfile", "keyfile", "password"], "arg_types": ["_ssl._SSLContext", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_ssl._PasswordType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_cert_chain of _SSLContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_dh_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl._SSLContext.load_dh_params", "name": "load_dh_params", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_ssl._SSLContext", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_dh_params of _SSLContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_verify_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "cafile", "<PERSON><PERSON>", "cadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl._SSLContext.load_verify_locations", "name": "load_verify_locations", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "cafile", "<PERSON><PERSON>", "cadata"], "arg_types": ["_ssl._SSLContext", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "_collections_abc.<PERSON><PERSON>er", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_verify_locations of _SSLContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "maximum_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ssl._SSLContext.maximum_version", "name": "maximum_version", "type": "builtins.int"}}, "minimum_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ssl._SSLContext.minimum_version", "name": "minimum_version", "type": "builtins.int"}}, "num_tickets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ssl._SSLContext.num_tickets", "name": "num_tickets", "type": "builtins.int"}}, "options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ssl._SSLContext.options", "name": "options", "type": "builtins.int"}}, "post_handshake_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ssl._SSLContext.post_handshake_auth", "name": "post_handshake_auth", "type": "builtins.bool"}}, "protocol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ssl._SSLContext.protocol", "name": "protocol", "type": "builtins.int"}}, "security_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ssl._SSLContext.security_level", "name": "security_level", "type": "builtins.int"}}, "session_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl._SSLContext.session_stats", "name": "session_stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_ssl._SSLContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "session_stats of _SSLContext", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_ciphers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl._SSLContext.set_ciphers", "name": "set_ciphers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_ssl._SSLContext", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_ciphers of _SSLContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_default_verify_paths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl._SSLContext.set_default_verify_paths", "name": "set_default_verify_paths", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_ssl._SSLContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_default_verify_paths of _SSLContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_ecdh_curve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl._SSLContext.set_ecdh_curve", "name": "set_ecdh_curve", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_ssl._SSLContext", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_ecdh_curve of _SSLContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sni_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ssl._SSLContext.sni_callback", "name": "sni_callback", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["ssl.SSLObject", "builtins.str", "ssl.SSLContext"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "verify_flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ssl._SSLContext.verify_flags", "name": "verify_flags", "type": "builtins.int"}}, "verify_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_ssl._SSLContext.verify_mode", "name": "verify_mode", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_ssl._SSLContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_ssl._SSLContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_ssl.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "enum_certificates": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["store_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl.enum_certificates", "name": "enum_certificates", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["store_name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enum_certificates", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_ssl._EnumRetType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enum_crls": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["store_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl.enum_crls", "name": "enum_crls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["store_name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enum_crls", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_ssl._EnumRetType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_default_verify_paths": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl.get_default_verify_paths", "name": "get_default_verify_paths", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_default_verify_paths", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nid2obj": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl.nid2obj", "name": "nid2obj", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nid2obj", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "txt2obj": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["txt", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_ssl.txt2obj", "name": "txt2obj", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["txt", "name"], "arg_types": ["builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "txt2obj", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\_ssl.pyi"}