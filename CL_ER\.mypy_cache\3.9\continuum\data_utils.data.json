{".class": "MypyFile", "_fullname": "continuum.data_utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.data_utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.data_utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.data_utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.data_utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.data_utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.data_utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "create_task_composition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["class_nums", "num_tasks", "fixed_order"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.data_utils.create_task_composition", "name": "create_task_composition", "type": null}}, "data": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data", "kind": "Gdef"}, "dataset_transform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.Dataset"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "continuum.data_utils.dataset_transform", "name": "dataset_transform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "continuum.data_utils.dataset_transform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "continuum.data_utils", "mro": ["continuum.data_utils.dataset_transform", "torch.utils.data.dataset.Dataset", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.data_utils.dataset_transform.__getitem__", "name": "__getitem__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "x", "y", "transform"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.data_utils.dataset_transform.__init__", "name": "__init__", "type": null}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.data_utils.dataset_transform.__len__", "name": "__len__", "type": null}}, "transform": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.data_utils.dataset_transform.transform", "name": "transform", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "x": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.data_utils.dataset_transform.x", "name": "x", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "y": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.data_utils.dataset_transform.y", "name": "y", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "continuum.data_utils.dataset_transform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "continuum.data_utils.dataset_transform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "load_task_with_labels": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "y", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.data_utils.load_task_with_labels", "name": "load_task_with_labels", "type": null}}, "load_task_with_labels_torch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "y", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.data_utils.load_task_with_labels_torch", "name": "load_task_with_labels_torch", "type": null}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "setup_test_loader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["test_data", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.data_utils.setup_test_loader", "name": "setup_test_loader", "type": null}}, "shuffle_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.data_utils.shuffle_data", "name": "shuffle_data", "type": null}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "train_val_test_split_ni": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["train_data", "train_label", "test_data", "test_label"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.data_utils.train_val_test_split_ni", "name": "train_val_test_split_ni", "type": null}}, "transforms_match": {".class": "SymbolTableNode", "cross_ref": "utils.setup_elements.transforms_match", "kind": "Gdef"}}, "path": "D:\\continual_learning\\CL_ER\\continuum\\data_utils.py"}