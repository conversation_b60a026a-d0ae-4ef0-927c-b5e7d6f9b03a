{"data_mtime": 1754286305, "dep_lines": [1, 2, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 30, 30, 30], "dependencies": ["abc", "os", "builtins", "_frozen_importlib", "ntpath", "typing"], "hash": "f19af389ea511832a6c7a8be27ead20f67ba6845", "id": "continuum.dataset_scripts.dataset_base", "ignore_all": true, "interface_hash": "f5a532eb8ec6727fd3c05dfd94e90dbd4896f740", "mtime": 1733894459, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\continuum\\dataset_scripts\\dataset_base.py", "plugin_data": null, "size": 1085, "suppressed": [], "version_id": "1.15.0"}