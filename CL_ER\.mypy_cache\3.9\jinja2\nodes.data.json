{".class": "MypyFile", "_fullname": "jinja2.nodes", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Add": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.BinExpr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Add", "name": "Add", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Add", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Add", "jinja2.nodes.BinExpr", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Add.operator", "name": "operator", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Add.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Add", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "And": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.BinExpr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.And", "name": "And", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.And", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.And", "jinja2.nodes.BinExpr", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.And.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.And", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of And", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.And.operator", "name": "operator", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.And.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.And", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Assign": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Assign", "name": "Assign", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Assign", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Assign", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Assign.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Assign.node", "name": "node", "type": "jinja2.nodes.Node"}}, "target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Assign.target", "name": "target", "type": "jinja2.nodes.Expr"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Assign.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Assign", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AssignBlock": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.AssignBlock", "name": "As<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.AssignBlock", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.AssignBlock", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.AssignBlock.body", "name": "body", "type": {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.AssignBlock.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.AssignBlock.filter", "name": "filter", "type": {".class": "UnionType", "items": ["jinja2.nodes.Filter", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.AssignBlock.target", "name": "target", "type": "jinja2.nodes.Expr"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.AssignBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.AssignBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BinExpr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.BinExpr", "name": "BinExpr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.BinExpr", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.BinExpr", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "abstract": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.BinExpr.abstract", "name": "abstract", "type": "builtins.bool"}}, "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.BinExpr.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.BinExpr", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of BinExpr", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.BinExpr.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "left": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.BinExpr.left", "name": "left", "type": "jinja2.nodes.Expr"}}, "operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.BinExpr.operator", "name": "operator", "type": "builtins.str"}}, "right": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.BinExpr.right", "name": "right", "type": "jinja2.nodes.Expr"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.BinExpr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.BinExpr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Block": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Block", "name": "Block", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Block", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Block", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Block.body", "name": "body", "type": {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Block.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Block.name", "name": "name", "type": "builtins.str"}}, "required": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Block.required", "name": "required", "type": "builtins.bool"}}, "scoped": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Block.scoped", "name": "scoped", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Block.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Block", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Break": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Break", "name": "Break", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Break", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Break", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Break.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Break", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Call": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Call", "name": "Call", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Call", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Call", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Call.args", "name": "args", "type": {".class": "Instance", "args": ["jinja2.nodes.Expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "dyn_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Call.dyn_args", "name": "dyn_args", "type": {".class": "UnionType", "items": ["jinja2.nodes.Expr", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "dyn_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Call.dyn_kwargs", "name": "dyn_kwargs", "type": {".class": "UnionType", "items": ["jinja2.nodes.Expr", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Call.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Call.kwargs", "name": "kwargs", "type": {".class": "Instance", "args": ["jinja2.nodes.Keyword"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Call.node", "name": "node", "type": "jinja2.nodes.Expr"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Call.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Call", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CallBlock": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.CallBlock", "name": "CallBlock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.CallBlock", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.CallBlock", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.CallBlock.args", "name": "args", "type": {".class": "Instance", "args": ["jinja2.nodes.Name"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.CallBlock.body", "name": "body", "type": {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.CallBlock.call", "name": "call", "type": "jinja2.nodes.Call"}}, "defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.CallBlock.defaults", "name": "defaults", "type": {".class": "Instance", "args": ["jinja2.nodes.Expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.CallBlock.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.CallBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.CallBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Compare": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Compare", "name": "Compare", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Compare", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Compare", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Compare.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.Compare", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of Compare", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Compare.expr", "name": "expr", "type": "jinja2.nodes.Expr"}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Compare.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Compare.ops", "name": "ops", "type": {".class": "Instance", "args": ["jinja2.nodes.Operand"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Compare.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Compare", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Concat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Concat", "name": "Concat", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Concat", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Concat", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Concat.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.Concat", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of Concat", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Concat.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Concat.nodes", "name": "nodes", "type": {".class": "Instance", "args": ["jinja2.nodes.Expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Concat.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Concat", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CondExpr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.CondExpr", "name": "CondExpr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.CondExpr", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.CondExpr", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.CondExpr.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.CondExpr", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of CondExpr", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "expr1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.CondExpr.expr1", "name": "expr1", "type": "jinja2.nodes.Expr"}}, "expr2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.CondExpr.expr2", "name": "expr2", "type": {".class": "UnionType", "items": ["jinja2.nodes.Expr", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.CondExpr.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.CondExpr.test", "name": "test", "type": "jinja2.nodes.Expr"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.CondExpr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.CondExpr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Const": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Literal"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Const", "name": "Const", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Const", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Const", "jinja2.nodes.Literal", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Const.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.Const", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of Const", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Const.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "from_untrusted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "value", "lineno", "environment"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "jinja2.nodes.Const.from_untrusted", "name": "from_untrusted", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "value", "lineno", "environment"], "arg_types": [{".class": "TypeType", "item": "jinja2.nodes.Const"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["jinja2.environment.Environment", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_untrusted of Const", "ret_type": "jinja2.nodes.Const", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "jinja2.nodes.Const.from_untrusted", "name": "from_untrusted", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "value", "lineno", "environment"], "arg_types": [{".class": "TypeType", "item": "jinja2.nodes.Const"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["jinja2.environment.Environment", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_untrusted of Const", "ret_type": "jinja2.nodes.Const", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Const.value", "name": "value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Const.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Const", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ContextReference": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.ContextReference", "name": "ContextReference", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.ContextReference", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.ContextReference", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.ContextReference.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.ContextReference", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Continue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Continue", "name": "Continue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Continue", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Continue", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Continue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Continue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DerivedContextReference": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.DerivedContextReference", "name": "DerivedContextReference", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.DerivedContextReference", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.DerivedContextReference", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.DerivedContextReference.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.DerivedContextReference", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Literal"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Dict", "name": "Dict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Dict", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Dict", "jinja2.nodes.Literal", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Dict.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.Dict", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of Dict", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Dict.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Dict.items", "name": "items", "type": {".class": "Instance", "args": ["jinja2.nodes.Pair"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Dict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Dict", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Div": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.BinExpr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Div", "name": "Div", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Div", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Div", "jinja2.nodes.BinExpr", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Div.operator", "name": "operator", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Div.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Div", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Environment": {".class": "SymbolTableNode", "cross_ref": "jinja2.environment.Environment", "kind": "Gdef"}, "EnvironmentAttribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.EnvironmentAttribute", "name": "EnvironmentAttribute", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.EnvironmentAttribute", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.EnvironmentAttribute", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.EnvironmentAttribute.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.EnvironmentAttribute.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.EnvironmentAttribute.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.EnvironmentAttribute", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EvalContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.EvalContext", "name": "EvalContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.EvalContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.EvalContext", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "environment", "template_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.EvalContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "environment", "template_name"], "arg_types": ["jinja2.nodes.EvalContext", "jinja2.environment.Environment", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EvalContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "autoescape": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.nodes.EvalContext.autoescape", "name": "autoescape", "type": "builtins.bool"}}, "environment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.nodes.EvalContext.environment", "name": "environment", "type": "jinja2.environment.Environment"}}, "revert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "old"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.EvalContext.revert", "name": "revert", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "old"], "arg_types": ["jinja2.nodes.EvalContext", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "revert of EvalContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.EvalContext.save", "name": "save", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.nodes.EvalContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save of EvalContext", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "volatile": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.nodes.EvalContext.volatile", "name": "volatile", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.EvalContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.EvalContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EvalContextModifier": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.EvalContextModifier", "name": "EvalContextModifier", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.EvalContextModifier", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.EvalContextModifier", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.EvalContextModifier.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.EvalContextModifier.options", "name": "options", "type": {".class": "Instance", "args": ["jinja2.nodes.Keyword"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.EvalContextModifier.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.EvalContextModifier", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Expr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Node"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Expr", "name": "Expr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Expr", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "abstract": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Expr.abstract", "name": "abstract", "type": "builtins.bool"}}, "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Expr.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.Expr", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of Expr", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_assign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Expr.can_assign", "name": "can_assign", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.nodes.Expr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_assign of Expr", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Expr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Expr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExprStmt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.ExprStmt", "name": "ExprStmt", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.ExprStmt", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.ExprStmt", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.ExprStmt.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.ExprStmt.node", "name": "node", "type": "jinja2.nodes.Node"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.ExprStmt.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.ExprStmt", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Extends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Extends", "name": "Extends", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Extends", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Extends", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Extends.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Extends.template", "name": "template", "type": "jinja2.nodes.Expr"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Extends.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Extends", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExtensionAttribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.ExtensionAttribute", "name": "ExtensionAttribute", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.ExtensionAttribute", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.ExtensionAttribute", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.ExtensionAttribute.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "identifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.ExtensionAttribute.identifier", "name": "identifier", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.ExtensionAttribute.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.ExtensionAttribute.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.ExtensionAttribute", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Filter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes._FilterTestCommon"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Filter", "name": "Filter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Filter", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Filter", "jinja2.nodes._FilterTestCommon", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Filter.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.Filter", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of Filter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Filter.node", "name": "node", "type": {".class": "UnionType", "items": ["jinja2.nodes.Expr", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Filter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Filter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FilterBlock": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.FilterBlock", "name": "FilterBlock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.FilterBlock", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.FilterBlock", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.FilterBlock.body", "name": "body", "type": {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.FilterBlock.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.FilterBlock.filter", "name": "filter", "type": "jinja2.nodes.Filter"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.FilterBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.FilterBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FloorDiv": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.BinExpr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.FloorDiv", "name": "FloorDiv", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.FloorDiv", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.FloorDiv", "jinja2.nodes.BinExpr", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.FloorDiv.operator", "name": "operator", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.FloorDiv.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.FloorDiv", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "For": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.For", "name": "For", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.For", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.For", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.For.body", "name": "body", "type": {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "else_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.For.else_", "name": "else_", "type": {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.For.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "iter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.For.iter", "name": "iter", "type": "jinja2.nodes.Node"}}, "recursive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.For.recursive", "name": "recursive", "type": "builtins.bool"}}, "target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.For.target", "name": "target", "type": "jinja2.nodes.Node"}}, "test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.For.test", "name": "test", "type": {".class": "UnionType", "items": ["jinja2.nodes.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.For.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.For", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FromImport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.FromImport", "name": "FromImport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.FromImport", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.FromImport", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.FromImport.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.FromImport.names", "name": "names", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.FromImport.template", "name": "template", "type": "jinja2.nodes.Expr"}}, "with_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.FromImport.with_context", "name": "with_context", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.FromImport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.FromImport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Getattr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Getattr", "name": "Getattr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Getattr", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Getattr", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Getattr.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.Getattr", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of Getattr", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Getattr.attr", "name": "attr", "type": "builtins.str"}}, "ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Getattr.ctx", "name": "ctx", "type": "builtins.str"}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Getattr.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Getattr.node", "name": "node", "type": "jinja2.nodes.Expr"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Getattr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Getattr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Getitem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Getitem", "name": "Getitem", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Getitem", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Getitem", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Getitem.arg", "name": "arg", "type": "jinja2.nodes.Expr"}}, "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Getitem.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.Getitem", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of Getitem", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Getitem.ctx", "name": "ctx", "type": "builtins.str"}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Getitem.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Getitem.node", "name": "node", "type": "jinja2.nodes.Expr"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Getitem.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Getitem", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Helper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Node"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Helper", "name": "Helper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Helper", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Helper", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "abstract": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Helper.abstract", "name": "abstract", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Helper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Helper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "If": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.If", "name": "If", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.If", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.If", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.If.body", "name": "body", "type": {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "elif_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.If.elif_", "name": "elif_", "type": {".class": "Instance", "args": ["jinja2.nodes.If"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "else_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.If.else_", "name": "else_", "type": {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.If.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.If.test", "name": "test", "type": "jinja2.nodes.Node"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.If.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.If", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Import": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Import", "name": "Import", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Import", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Import", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Import.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Import.target", "name": "target", "type": "builtins.str"}}, "template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Import.template", "name": "template", "type": "jinja2.nodes.Expr"}}, "with_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Import.with_context", "name": "with_context", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Import.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Import", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImportedName": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.ImportedName", "name": "ImportedName", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.ImportedName", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.ImportedName", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.ImportedName.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "importname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.ImportedName.importname", "name": "importname", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.ImportedName.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.ImportedName", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Impossible": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Impossible", "name": "Impossible", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Impossible", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Impossible", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Impossible.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Impossible", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Include": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Include", "name": "Include", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Include", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Include", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Include.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "ignore_missing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Include.ignore_missing", "name": "ignore_missing", "type": "builtins.bool"}}, "template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Include.template", "name": "template", "type": "jinja2.nodes.Expr"}}, "with_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Include.with_context", "name": "with_context", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Include.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Include", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InternalName": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.InternalName", "name": "InternalName", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.InternalName", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.InternalName", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.InternalName.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.nodes.InternalName"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InternalName", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.InternalName.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.InternalName.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.InternalName.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.InternalName", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Keyword": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Helper"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Keyword", "name": "Keyword", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Keyword", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Keyword", "jinja2.nodes.Helper", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Keyword.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.Keyword", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of Keyword", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Keyword.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Keyword.key", "name": "key", "type": "builtins.str"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Keyword.value", "name": "value", "type": "jinja2.nodes.Expr"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Keyword.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Keyword", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Literal"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.List", "name": "List", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.List", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.List", "jinja2.nodes.Literal", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.List.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.List", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of List", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.List.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.List.items", "name": "items", "type": {".class": "Instance", "args": ["jinja2.nodes.Expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.List.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.List", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Literal", "name": "Literal", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Literal", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Literal", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "abstract": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Literal.abstract", "name": "abstract", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Literal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Literal", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Macro": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Macro", "name": "Macro", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Macro", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Macro", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Macro.args", "name": "args", "type": {".class": "Instance", "args": ["jinja2.nodes.Name"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Macro.body", "name": "body", "type": {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Macro.defaults", "name": "defaults", "type": {".class": "Instance", "args": ["jinja2.nodes.Expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Macro.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Macro.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Macro.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Macro", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MarkSafe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.MarkSafe", "name": "MarkSafe", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.MarkSafe", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.MarkSafe", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.MarkSafe.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.MarkSafe", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of MarkSafe", "ret_type": "markupsafe.Markup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.MarkSafe.expr", "name": "expr", "type": "jinja2.nodes.Expr"}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.MarkSafe.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.MarkSafe.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.MarkSafe", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MarkSafeIfAutoescape": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.MarkSafeIfAutoescape", "name": "MarkSafeIfAutoescape", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.MarkSafeIfAutoescape", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.MarkSafeIfAutoescape", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.MarkSafeIfAutoescape.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.MarkSafeIfAutoescape", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of MarkSafeIfAutoescape", "ret_type": {".class": "UnionType", "items": ["markupsafe.Markup", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.MarkSafeIfAutoescape.expr", "name": "expr", "type": "jinja2.nodes.Expr"}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.MarkSafeIfAutoescape.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.MarkSafeIfAutoescape.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.MarkSafeIfAutoescape", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Markup": {".class": "SymbolTableNode", "cross_ref": "markupsafe.Markup", "kind": "Gdef"}, "Mod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.BinExpr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Mod", "name": "Mod", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Mod", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Mod", "jinja2.nodes.BinExpr", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Mod.operator", "name": "operator", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Mod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Mod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mul": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.BinExpr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Mul", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Mul", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Mul", "jinja2.nodes.BinExpr", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Mul.operator", "name": "operator", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Mul.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Mul", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NSRef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.NSRef", "name": "NSRef", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.NSRef", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.NSRef", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "attr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.NSRef.attr", "name": "attr", "type": "builtins.str"}}, "can_assign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.NSRef.can_assign", "name": "can_assign", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.nodes.NSRef"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_assign of NSRef", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.NSRef.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.NSRef.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.NSRef.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.NSRef", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Name", "name": "Name", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Name", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Name", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "can_assign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Name.can_assign", "name": "can_assign", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.nodes.Name"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_assign of Name", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Name.ctx", "name": "ctx", "type": "builtins.str"}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Name.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Name.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Name.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Name", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Neg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.UnaryExpr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Neg", "name": "Neg", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Neg", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Neg", "jinja2.nodes.UnaryExpr", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Neg.operator", "name": "operator", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Neg.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Neg", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "jinja2.nodes.NodeType", "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Node", "name": "Node", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Node", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Node.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["jinja2.nodes.Node", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of Node", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Node.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "fields", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Node.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "fields", "attributes"], "arg_types": ["jinja2.nodes.Node", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Node", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Node.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["jinja2.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Node", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "abstract": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Node.abstract", "name": "abstract", "type": "builtins.bool"}}, "attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "jinja2.nodes.Node.attributes", "name": "attributes", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "dump": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Node.dump", "name": "dump", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dump of Node", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "environment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Node.environment", "name": "environment", "type": {".class": "UnionType", "items": ["jinja2.environment.Environment", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "jinja2.nodes.Node.fields", "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "find": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Node.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node_type"], "arg_types": ["jinja2.nodes.Node", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes._NodeBound", "id": -1, "name": "_NodeBound", "namespace": "jinja2.nodes.Node.find", "upper_bound": "jinja2.nodes.Node", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find of Node", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes._NodeBound", "id": -1, "name": "_NodeBound", "namespace": "jinja2.nodes.Node.find", "upper_bound": "jinja2.nodes.Node", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes._NodeBound", "id": -1, "name": "_NodeBound", "namespace": "jinja2.nodes.Node.find", "upper_bound": "jinja2.nodes.Node", "values": [], "variance": 0}]}}}, "find_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Node.find_all", "name": "find_all", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node_type"], "arg_types": ["jinja2.nodes.Node", {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes._NodeBound", "id": -1, "name": "_NodeBound", "namespace": "jinja2.nodes.Node.find_all", "upper_bound": "jinja2.nodes.Node", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes._NodeBound", "id": -1, "name": "_NodeBound", "namespace": "jinja2.nodes.Node.find_all", "upper_bound": "jinja2.nodes.Node", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_all of Node", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes._NodeBound", "id": -1, "name": "_NodeBound", "namespace": "jinja2.nodes.Node.find_all", "upper_bound": "jinja2.nodes.Node", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes._NodeBound", "id": -1, "name": "_NodeBound", "namespace": "jinja2.nodes.Node.find_all", "upper_bound": "jinja2.nodes.Node", "values": [], "variance": 0}]}}}, "iter_child_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "exclude", "only"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Node.iter_child_nodes", "name": "iter_child_nodes", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "exclude", "only"], "arg_types": ["jinja2.nodes.Node", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Container"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Container"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_child_nodes of Node", "ret_type": {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "exclude", "only"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Node.iter_fields", "name": "iter_fields", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "exclude", "only"], "arg_types": ["jinja2.nodes.Node", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Container"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Container"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_fields of Node", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Node.lineno", "name": "lineno", "type": "builtins.int"}}, "set_ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Node.set_ctx", "name": "set_ctx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ctx"], "arg_types": ["jinja2.nodes.Node", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_ctx of Node", "ret_type": "jinja2.nodes.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_environment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "environment"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Node.set_environment", "name": "set_environment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "environment"], "arg_types": ["jinja2.nodes.Node", "jinja2.environment.Environment"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_environment of Node", "ret_type": "jinja2.nodes.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "lineno", "override"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Node.set_lineno", "name": "set_lineno", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "lineno", "override"], "arg_types": ["jinja2.nodes.Node", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_lineno of Node", "ret_type": "jinja2.nodes.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Node.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Node", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NodeType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.NodeType", "name": "NodeType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.NodeType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.NodeType", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["mcs", "name", "bases", "d"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "jinja2.nodes.NodeType.__new__", "name": "__new__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.NodeType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.NodeType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Not": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.UnaryExpr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Not", "name": "Not", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Not", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Not", "jinja2.nodes.UnaryExpr", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Not.operator", "name": "operator", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Not.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Not", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Operand": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Helper"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Operand", "name": "Operand", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Operand", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Operand", "jinja2.nodes.Helper", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Operand.expr", "name": "expr", "type": "jinja2.nodes.Expr"}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Operand.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Operand.op", "name": "op", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Operand.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Operand", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Or": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.BinExpr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Or", "name": "Or", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Or", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Or", "jinja2.nodes.BinExpr", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Or.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.Or", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of Or", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Or.operator", "name": "operator", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Or.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Or", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Output": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Output", "name": "Output", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Output", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Output", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Output.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Output.nodes", "name": "nodes", "type": {".class": "Instance", "args": ["jinja2.nodes.Expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Output.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Output", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OverlayScope": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.OverlayScope", "name": "OverlayScope", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.OverlayScope", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.OverlayScope", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.OverlayScope.body", "name": "body", "type": {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.OverlayScope.context", "name": "context", "type": "jinja2.nodes.Expr"}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.OverlayScope.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.OverlayScope.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.OverlayScope", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Pair": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Helper"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Pair", "name": "Pair", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Pair", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Pair", "jinja2.nodes.Helper", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Pair.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.Pair", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of Pair", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Pair.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Pair.key", "name": "key", "type": "jinja2.nodes.Expr"}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Pair.value", "name": "value", "type": "jinja2.nodes.Expr"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Pair.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Pair", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Pos": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.UnaryExpr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Pos", "name": "Pos", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Pos", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Pos", "jinja2.nodes.UnaryExpr", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Pos.operator", "name": "operator", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Pos.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Pos", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Pow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.BinExpr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Pow", "name": "<PERSON>w", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Pow", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Pow", "jinja2.nodes.BinExpr", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Pow.operator", "name": "operator", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Pow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Pow", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Scope": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Scope", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Scope", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Scope", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Scope.body", "name": "body", "type": {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Scope.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Scope.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Scope", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ScopedEvalContextModifier": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.EvalContextModifier"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.ScopedEvalContextModifier", "name": "ScopedEvalContextModifier", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.ScopedEvalContextModifier", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.ScopedEvalContextModifier", "jinja2.nodes.EvalContextModifier", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.ScopedEvalContextModifier.body", "name": "body", "type": {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.ScopedEvalContextModifier.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.ScopedEvalContextModifier.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.ScopedEvalContextModifier", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Slice": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Slice", "name": "Slice", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Slice", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Slice", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Slice.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.Slice", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of Slice", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Slice.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Slice.start", "name": "start", "type": {".class": "UnionType", "items": ["jinja2.nodes.Expr", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Slice.step", "name": "step", "type": {".class": "UnionType", "items": ["jinja2.nodes.Expr", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "stop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Slice.stop", "name": "stop", "type": {".class": "UnionType", "items": ["jinja2.nodes.Expr", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Slice.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Slice", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Stmt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Node"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Stmt", "name": "Stmt", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Stmt", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "abstract": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Stmt.abstract", "name": "abstract", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Stmt.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Stmt", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sub": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.BinExpr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Sub", "name": "Sub", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Sub", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Sub", "jinja2.nodes.BinExpr", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Sub.operator", "name": "operator", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Sub.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Sub", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Node"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Template", "name": "Template", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Template", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Template", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Template.body", "name": "body", "type": {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Template.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Template.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Template", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TemplateData": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Literal"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.TemplateData", "name": "TemplateData", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.TemplateData", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.TemplateData", "jinja2.nodes.Literal", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.TemplateData.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.TemplateData", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of TemplateData", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.TemplateData.data", "name": "data", "type": "builtins.str"}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.TemplateData.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.TemplateData.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.TemplateData", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Test": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes._FilterTestCommon"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Test", "name": "Test", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Test", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Test", "jinja2.nodes._FilterTestCommon", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "_is_filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Test._is_filter", "name": "_is_filter", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Test.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Test", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Literal"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.Tuple", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Tuple", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.Tuple", "jinja2.nodes.Literal", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Tuple.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.Tuple", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of <PERSON><PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_assign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.Tuple.can_assign", "name": "can_assign", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.nodes.Tuple"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_assign of <PERSON>ple", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Tuple.ctx", "name": "ctx", "type": "builtins.str"}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.Tuple.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.Tuple.items", "name": "items", "type": {".class": "Instance", "args": ["jinja2.nodes.Expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.Tuple.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.Tuple", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnaryExpr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.UnaryExpr", "name": "UnaryExpr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.UnaryExpr", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.UnaryExpr", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "abstract": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.UnaryExpr.abstract", "name": "abstract", "type": "builtins.bool"}}, "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.UnaryExpr.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes.UnaryExpr", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of UnaryExpr", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.UnaryExpr.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.UnaryExpr.node", "name": "node", "type": "jinja2.nodes.Expr"}}, "operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.UnaryExpr.operator", "name": "operator", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.UnaryExpr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.UnaryExpr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "With": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Stmt"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes.With", "name": "With", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes.With", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes.With", "jinja2.nodes.Stmt", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.With.body", "name": "body", "type": {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes.With.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "targets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.With.targets", "name": "targets", "type": {".class": "Instance", "args": ["jinja2.nodes.Expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes.With.values", "name": "values", "type": {".class": "Instance", "args": ["jinja2.nodes.Expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes.With.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes.With", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_FilterTestCommon": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.nodes.Expr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.nodes._FilterTestCommon", "name": "_FilterTestCommon", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.nodes._FilterTestCommon", "has_param_spec_type": false, "metaclass_type": "jinja2.nodes.NodeType", "metadata": {}, "module_name": "jinja2.nodes", "mro": ["jinja2.nodes._FilterTestCommon", "jinja2.nodes.Expr", "jinja2.nodes.Node", "builtins.object"], "names": {".class": "SymbolTable", "_is_filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes._FilterTestCommon._is_filter", "name": "_is_filter", "type": "builtins.bool"}}, "abstract": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes._FilterTestCommon.abstract", "name": "abstract", "type": "builtins.bool"}}, "args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes._FilterTestCommon.args", "name": "args", "type": {".class": "Instance", "args": ["jinja2.nodes.Expr"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "as_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes._FilterTestCommon.as_const", "name": "as_const", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_ctx"], "arg_types": ["jinja2.nodes._FilterTestCommon", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_const of _FilterTestCommon", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dyn_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes._FilterTestCommon.dyn_args", "name": "dyn_args", "type": {".class": "UnionType", "items": ["jinja2.nodes.Expr", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "dyn_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes._FilterTestCommon.dyn_kwargs", "name": "dyn_kwargs", "type": {".class": "UnionType", "items": ["jinja2.nodes.Expr", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.nodes._FilterTestCommon.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes._FilterTestCommon.kwargs", "name": "kwargs", "type": {".class": "Instance", "args": ["jinja2.nodes.Pair"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes._FilterTestCommon.name", "name": "name", "type": "builtins.str"}}, "node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.nodes._FilterTestCommon.node", "name": "node", "type": "jinja2.nodes.Expr"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes._FilterTestCommon.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.nodes._FilterTestCommon", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_NodeBound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.nodes._NodeBound", "name": "_NodeBound", "upper_bound": "jinja2.nodes.Node", "values": [], "variance": 0}}, "_PassArg": {".class": "SymbolTableNode", "cross_ref": "jinja2.utils._PassArg", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.nodes.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.nodes.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.nodes.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.nodes.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.nodes.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.nodes.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_binop_to_func": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "jinja2.nodes._binop_to_func", "name": "_binop_to_func", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_cmpop_to_func": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "jinja2.nodes._cmpop_to_func", "name": "_cmpop_to_func", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_failing_new": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes._failing_new", "name": "_failing_new", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_failing_new", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_uaop_to_func": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "jinja2.nodes._uaop_to_func", "name": "_uaop_to_func", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "args_as_const": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["node", "eval_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.args_as_const", "name": "args_as_const", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["node", "eval_ctx"], "arg_types": [{".class": "UnionType", "items": ["jinja2.nodes._FilterTestCommon", "jinja2.nodes.Call"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "args_as_const", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef"}, "get_eval_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["node", "ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.nodes.get_eval_context", "name": "get_eval_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["node", "ctx"], "arg_types": ["jinja2.nodes.Node", {".class": "UnionType", "items": ["jinja2.nodes.EvalContext", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_eval_context", "ret_type": "jinja2.nodes.EvalContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "te": {".class": "SymbolTableNode", "cross_ref": "typing_extensions", "kind": "Gdef"}}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\jinja2\\nodes.py"}