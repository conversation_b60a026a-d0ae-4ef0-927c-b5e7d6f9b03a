{".class": "MypyFile", "_fullname": "agents.hybrid", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ExperienceReplay": {".class": "SymbolTableNode", "cross_ref": "agents.exp_replay.ExperienceReplay", "kind": "Gdef"}, "F": {".class": "SymbolTableNode", "cross_ref": "torch.nn.functional", "kind": "Gdef"}, "HybridUnlearningAgent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["agents.exp_replay.ExperienceReplay"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "agents.hybrid.HybridUnlearningAgent", "name": "HybridUnlearningAgent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "agents.hybrid.HybridUnlearningAgent", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "agents.hybrid", "mro": ["agents.hybrid.HybridUnlearningAgent", "agents.exp_replay.ExperienceReplay", "agents.base.ContinualLearner", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "optimizer", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.hybrid.HybridUnlearningAgent.__init__", "name": "__init__", "type": null}}, "_adversarial_unlearning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "forget_x", "forget_y", "retain_x", "retain_y", "full_teacher", "bad_teacher", "forget_task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.hybrid.HybridUnlearningAgent._adversarial_unlearning", "name": "_adversarial_unlearning", "type": null}}, "_create_bad_teacher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "forget_task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.hybrid.HybridUnlearningAgent._create_bad_teacher", "name": "_create_bad_teacher", "type": null}}, "_evaluate_on_test_loaders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "test_loaders"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.hybrid.HybridUnlearningAgent._evaluate_on_test_loaders", "name": "_evaluate_on_test_loaders", "type": null}}, "_fine_tune_on_retain_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "retain_x", "retain_y", "teacher_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.hybrid.HybridUnlearningAgent._fine_tune_on_retain_data", "name": "_fine_tune_on_retain_data", "type": null}}, "_generate_adversarial_examples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.hybrid.HybridUnlearningAgent._generate_adversarial_examples", "name": "_generate_adversarial_examples", "type": null}}, "_get_task_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.hybrid.HybridUnlearningAgent._get_task_data", "name": "_get_task_data", "type": null}}, "_hybrid_unlearning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "forget_task_id", "forget_x", "forget_y", "retain_x", "retain_y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.hybrid.HybridUnlearningAgent._hybrid_unlearning", "name": "_hybrid_unlearning", "type": null}}, "_identify_important_neurons": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "forget_x", "forget_y", "forget_task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.hybrid.HybridUnlearningAgent._identify_important_neurons", "name": "_identify_important_neurons", "type": null}}, "_load_test_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.hybrid.HybridUnlearningAgent._load_test_data", "name": "_load_test_data", "type": null}}, "_load_test_data_for_subject": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.hybrid.HybridUnlearningAgent._load_test_data_for_subject", "name": "_load_test_data_for_subject", "type": null}}, "_remove_task_from_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.hybrid.HybridUnlearningAgent._remove_task_from_buffer", "name": "_remove_task_from_buffer", "type": null}}, "_reset_neurons": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "important_neurons"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.hybrid.HybridUnlearningAgent._reset_neurons", "name": "_reset_neurons", "type": null}}, "epsilon": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.hybrid.HybridUnlearningAgent.epsilon", "name": "epsilon", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "evaluate_accuracy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.hybrid.HybridUnlearningAgent.evaluate_accuracy", "name": "evaluate_accuracy", "type": null}}, "evaluate_membership_inference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "forget_x", "forget_y", "retain_x", "retain_y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.hybrid.HybridUnlearningAgent.evaluate_membership_inference", "name": "evaluate_membership_inference", "type": null}}, "forget": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "forget_task_id", "subjects_to_keep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.hybrid.HybridUnlearningAgent.forget", "name": "forget", "type": null}}, "kl_temperature": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.hybrid.HybridUnlearningAgent.kl_temperature", "name": "kl_temperature", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "model_cls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.hybrid.HybridUnlearningAgent.model_cls", "name": "model_cls", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "model_encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.hybrid.HybridUnlearningAgent.model_encoder", "name": "model_encoder", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "retain_weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.hybrid.HybridUnlearningAgent.retain_weight", "name": "retain_weight", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "retention_ratio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.hybrid.HybridUnlearningAgent.retention_ratio", "name": "retention_ratio", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "unlearning_epochs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.hybrid.HybridUnlearningAgent.unlearning_epochs", "name": "unlearning_epochs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "agents.hybrid.HybridUnlearningAgent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "agents.hybrid.HybridUnlearningAgent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.hybrid.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.hybrid.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.hybrid.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.hybrid.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.hybrid.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.hybrid.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "D:\\continual_learning\\CL_ER\\agents\\hybrid.py"}