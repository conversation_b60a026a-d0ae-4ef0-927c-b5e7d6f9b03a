{"data_mtime": 1754286351, "dep_lines": [9, 10, 11, 12, 14, 17, 23, 25, 3, 4, 5, 7, 9, 21, 289, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 25, 25, 10, 10, 10, 5, 20, 25, 20, 5, 30, 30, 30, 30], "dependencies": ["jinja2.defaults", "jinja2.nodes", "jinja2.environment", "jinja2.exceptions", "jinja2.runtime", "jinja2.utils", "jinja2.lexer", "jinja2.parser", "pprint", "re", "typing", "markupsafe", "jinja2", "typing_extensions", "gettext", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum"], "hash": "60fdf06a70e1fab81ddaee2d4c59fcf9db5824da", "id": "jinja2.ext", "ignore_all": true, "interface_hash": "3fc88c3498328dcd851169955bb12a47b0f48c29", "mtime": 1716993775, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\jinja2\\ext.py", "plugin_data": null, "size": 31877, "suppressed": [], "version_id": "1.15.0"}