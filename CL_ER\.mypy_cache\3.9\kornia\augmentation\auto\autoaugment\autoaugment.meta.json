{"data_mtime": 1754286447, "dep_lines": [23, 27, 22, 24, 27, 20, 25, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 20, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["kornia.augmentation.auto.operations.policy", "kornia.augmentation.auto.autoaugment.ops", "kornia.augmentation.auto.base", "kornia.augmentation.container.params", "kornia.augmentation.auto.autoaugment", "torch.distributions", "kornia.core", "typing", "builtins", "string", "torch", "torch.distributed._functional_collectives", "types", "itertools", "contextlib", "traceback", "torch.distributed", "torch.nn", "html", "operator", "os", "re", "sys", "multiprocessing.reduction", "collections", "warnings", "pprint", "inspect", "copy", "math", "uuid", "_frozen_importlib", "abc", "kornia.augmentation.auto.operations", "kornia.augmentation.container", "kornia.augmentation.container.base", "kornia.core._backend", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.distributions.categorical", "torch.distributions.distribution", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module"], "hash": "abfbd5e90c15a98ce080adfdc57a5d349db20d34", "id": "kornia.augmentation.auto.autoaugment.autoaugment", "ignore_all": true, "interface_hash": "3f01ecc2dfd6b357d4a177830c552d9eb5306f94", "mtime": 1740555645, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\kornia\\augmentation\\auto\\autoaugment\\autoaugment.py", "plugin_data": null, "size": 7423, "suppressed": [], "version_id": "1.15.0"}