{".class": "MypyFile", "_fullname": "jinja2.bccache", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Bucket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.bccache.Bucket", "name": "Bucket", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.bccache.Bucket", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jinja2.bccache", "mro": ["jinja2.bccache.Bucket", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "environment", "key", "checksum"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.Bucket.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "environment", "key", "checksum"], "arg_types": ["jinja2.bccache.Bucket", "jinja2.environment.Environment", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON>et", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bytecode_from_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.Bucket.bytecode_from_string", "name": "bytecode_from_string", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "string"], "arg_types": ["jinja2.bccache.Bucket", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bytecode_from_string of Bucket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bytecode_to_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.Bucket.bytecode_to_string", "name": "bytecode_to_string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.bccache.Bucket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bytecode_to_string of Bucket", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "checksum": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.bccache.Bucket.checksum", "name": "checksum", "type": "builtins.str"}}, "code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.bccache.Bucket.code", "name": "code", "type": {".class": "UnionType", "items": ["types.CodeType", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "environment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.bccache.Bucket.environment", "name": "environment", "type": "jinja2.environment.Environment"}}, "key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.bccache.Bucket.key", "name": "key", "type": "builtins.str"}}, "load_bytecode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "f"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.Bucket.load_bytecode", "name": "load_bytecode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "f"], "arg_types": ["jinja2.bccache.Bucket", "typing.BinaryIO"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_bytecode of Bucket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.Bucket.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.bccache.Bucket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of Bucket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_bytecode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "f"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.Bucket.write_bytecode", "name": "write_bytecode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "f"], "arg_types": ["jinja2.bccache.Bucket", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_bytecode of Bucket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.bccache.Bucket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.bccache.Bucket", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BytecodeCache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.bccache.BytecodeCache", "name": "BytecodeCache", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.bccache.BytecodeCache", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jinja2.bccache", "mro": ["jinja2.bccache.BytecodeCache", "builtins.object"], "names": {".class": "SymbolTable", "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.BytecodeCache.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.bccache.BytecodeCache"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of BytecodeCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dump_bytecode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.BytecodeCache.dump_bytecode", "name": "dump_bytecode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "arg_types": ["jinja2.bccache.BytecodeCache", "jinja2.bccache.Bucket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dump_bytecode of BytecodeCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_bucket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "environment", "name", "filename", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.BytecodeCache.get_bucket", "name": "get_bucket", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "environment", "name", "filename", "source"], "arg_types": ["jinja2.bccache.BytecodeCache", "jinja2.environment.Environment", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bucket of BytecodeCache", "ret_type": "jinja2.bccache.Bucket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_cache_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.BytecodeCache.get_cache_key", "name": "get_cache_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "filename"], "arg_types": ["jinja2.bccache.BytecodeCache", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cache_key of BytecodeCache", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_source_checksum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.BytecodeCache.get_source_checksum", "name": "get_source_checksum", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "source"], "arg_types": ["jinja2.bccache.BytecodeCache", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_source_checksum of BytecodeCache", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_bytecode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.BytecodeCache.load_bytecode", "name": "load_bytecode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "arg_types": ["jinja2.bccache.BytecodeCache", "jinja2.bccache.Bucket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_bytecode of BytecodeCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_bucket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.BytecodeCache.set_bucket", "name": "set_bucket", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "arg_types": ["jinja2.bccache.BytecodeCache", "jinja2.bccache.Bucket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_bucket of BytecodeCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.bccache.BytecodeCache.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.bccache.BytecodeCache", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BytesIO": {".class": "SymbolTableNode", "cross_ref": "_io.BytesIO", "kind": "Gdef"}, "CodeType": {".class": "SymbolTableNode", "cross_ref": "types.CodeType", "kind": "Gdef"}, "Environment": {".class": "SymbolTableNode", "cross_ref": "jinja2.environment.Environment", "kind": "Gdef"}, "FileSystemBytecodeCache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.bccache.BytecodeCache"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.bccache.FileSystemBytecodeCache", "name": "FileSystemBytecodeCache", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.bccache.FileSystemBytecodeCache", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jinja2.bccache", "mro": ["jinja2.bccache.FileSystemBytecodeCache", "jinja2.bccache.BytecodeCache", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "directory", "pattern"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.FileSystemBytecodeCache.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "directory", "pattern"], "arg_types": ["jinja2.bccache.FileSystemBytecodeCache", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FileSystemBytecodeCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_cache_filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.FileSystemBytecodeCache._get_cache_filename", "name": "_get_cache_filename", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "arg_types": ["jinja2.bccache.FileSystemBytecodeCache", "jinja2.bccache.Bucket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_cache_filename of FileSystemBytecodeCache", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_default_cache_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.FileSystemBytecodeCache._get_default_cache_dir", "name": "_get_default_cache_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.bccache.FileSystemBytecodeCache"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_default_cache_dir of FileSystemBytecodeCache", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.FileSystemBytecodeCache.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.bccache.FileSystemBytecodeCache"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of FileSystemBytecodeCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "directory": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.bccache.FileSystemBytecodeCache.directory", "name": "directory", "type": "builtins.str"}}, "dump_bytecode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.FileSystemBytecodeCache.dump_bytecode", "name": "dump_bytecode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "arg_types": ["jinja2.bccache.FileSystemBytecodeCache", "jinja2.bccache.Bucket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dump_bytecode of FileSystemBytecodeCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_bytecode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.FileSystemBytecodeCache.load_bytecode", "name": "load_bytecode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "arg_types": ["jinja2.bccache.FileSystemBytecodeCache", "jinja2.bccache.Bucket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_bytecode of FileSystemBytecodeCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pattern": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.bccache.FileSystemBytecodeCache.pattern", "name": "pattern", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.bccache.FileSystemBytecodeCache.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.bccache.FileSystemBytecodeCache", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MemcachedBytecodeCache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.bccache.BytecodeCache"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.bccache.MemcachedBytecodeCache", "name": "MemcachedBytecodeCache", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.bccache.MemcachedBytecodeCache", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jinja2.bccache", "mro": ["jinja2.bccache.MemcachedBytecodeCache", "jinja2.bccache.BytecodeCache", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "client", "prefix", "timeout", "ignore_memcache_errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.MemcachedBytecodeCache.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "client", "prefix", "timeout", "ignore_memcache_errors"], "arg_types": ["jinja2.bccache.MemcachedBytecodeCache", "jinja2.bccache._MemcachedClient", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MemcachedBytecodeCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.bccache.MemcachedBytecodeCache.client", "name": "client", "type": "jinja2.bccache._MemcachedClient"}}, "dump_bytecode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.MemcachedBytecodeCache.dump_bytecode", "name": "dump_bytecode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "arg_types": ["jinja2.bccache.MemcachedBytecodeCache", "jinja2.bccache.Bucket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dump_bytecode of MemcachedBytecodeCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ignore_memcache_errors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.bccache.MemcachedBytecodeCache.ignore_memcache_errors", "name": "ignore_memcache_errors", "type": "builtins.bool"}}, "load_bytecode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.bccache.MemcachedBytecodeCache.load_bytecode", "name": "load_bytecode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "arg_types": ["jinja2.bccache.MemcachedBytecodeCache", "jinja2.bccache.Bucket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_bytecode of MemcachedBytecodeCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prefix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.bccache.MemcachedBytecodeCache.prefix", "name": "prefix", "type": "builtins.str"}}, "timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.bccache.MemcachedBytecodeCache.timeout", "name": "timeout", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.bccache.MemcachedBytecodeCache.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.bccache.MemcachedBytecodeCache", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MemcachedClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get", 2], ["set", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.bccache._MemcachedClient", "name": "_MemcachedClient", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "jinja2.bccache._MemcachedClient", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "jinja2.bccache", "mro": ["jinja2.bccache._MemcachedClient", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_mypy_only"], "fullname": "jinja2.bccache._MemcachedClient.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["jinja2.bccache._MemcachedClient", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of _MemcachedClient", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "key", "value", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_mypy_only"], "fullname": "jinja2.bccache._MemcachedClient.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "key", "value", "timeout"], "arg_types": ["jinja2.bccache._MemcachedClient", "builtins.str", "builtins.bytes", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set of _MemcachedClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.bccache._MemcachedClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.bccache._MemcachedClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.bccache.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.bccache.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.bccache.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.bccache.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.bccache.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.bccache.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "bc_magic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "jinja2.bccache.bc_magic", "name": "bc_magic", "type": "builtins.bytes"}}, "bc_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "jinja2.bccache.bc_version", "name": "bc_version", "type": "builtins.int"}}, "errno": {".class": "SymbolTableNode", "cross_ref": "errno", "kind": "Gdef"}, "fnmatch": {".class": "SymbolTableNode", "cross_ref": "fnmatch", "kind": "Gdef"}, "marshal": {".class": "SymbolTableNode", "cross_ref": "marshal", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "pickle": {".class": "SymbolTableNode", "cross_ref": "pickle", "kind": "Gdef"}, "sha1": {".class": "SymbolTableNode", "cross_ref": "_hashlib.openssl_sha1", "kind": "Gdef"}, "stat": {".class": "SymbolTableNode", "cross_ref": "stat", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "te": {".class": "SymbolTableNode", "cross_ref": "typing_extensions", "kind": "Gdef"}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef"}}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\jinja2\\bccache.py"}