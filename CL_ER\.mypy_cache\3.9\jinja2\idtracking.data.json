{".class": "MypyFile", "_fullname": "jinja2.idtracking", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "FrameSymbolVisitor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.visitor.NodeVisitor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.idtracking.FrameSymbolVisitor", "name": "FrameSymbolVisitor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.FrameSymbolVisitor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jinja2.idtracking", "mro": ["jinja2.idtracking.FrameSymbolVisitor", "jinja2.visitor.NodeVisitor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "symbols"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.FrameSymbolVisitor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "symbols"], "arg_types": ["jinja2.idtracking.FrameSymbolVisitor", "jinja2.idtracking.Symbols"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FrameSymbolVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "symbols": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.idtracking.FrameSymbolVisitor.symbols", "name": "symbols", "type": "jinja2.idtracking.Symbols"}}, "visit_Assign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.FrameSymbolVisitor.visit_Assign", "name": "visit_Assign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.FrameSymbolVisitor", "jinja2.nodes.Assign", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Assign of FrameSymbolVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_AssignBlock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.FrameSymbolVisitor.visit_AssignBlock", "name": "visit_AssignBlock", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.FrameSymbolVisitor", "jinja2.nodes.AssignBlock", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_AssignBlock of FrameSymbolVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Block": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.FrameSymbolVisitor.visit_Block", "name": "visit_Block", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.FrameSymbolVisitor", "jinja2.nodes.Block", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Block of FrameSymbolVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_CallBlock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.FrameSymbolVisitor.visit_CallBlock", "name": "visit_CallBlock", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.FrameSymbolVisitor", "jinja2.nodes.CallBlock", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_CallBlock of FrameSymbolVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_FilterBlock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.FrameSymbolVisitor.visit_FilterBlock", "name": "visit_FilterBlock", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.FrameSymbolVisitor", "jinja2.nodes.FilterBlock", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_FilterBlock of FrameSymbolVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_For": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.FrameSymbolVisitor.visit_For", "name": "visit_For", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.FrameSymbolVisitor", "jinja2.nodes.For", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_For of FrameSymbolVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_FromImport": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.FrameSymbolVisitor.visit_FromImport", "name": "visit_FromImport", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.FrameSymbolVisitor", "jinja2.nodes.FromImport", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_FromImport of FrameSymbolVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_If": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.FrameSymbolVisitor.visit_If", "name": "visit_If", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.FrameSymbolVisitor", "jinja2.nodes.If", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_If of FrameSymbolVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Import": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.FrameSymbolVisitor.visit_Import", "name": "visit_Import", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.FrameSymbolVisitor", "jinja2.nodes.Import", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Import of FrameSymbolVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Macro": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.FrameSymbolVisitor.visit_Macro", "name": "visit_<PERSON>ro", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.FrameSymbolVisitor", "jinja2.nodes.Macro", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Macro of FrameSymbolVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_NSRef": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.FrameSymbolVisitor.visit_NSRef", "name": "visit_NSRef", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.FrameSymbolVisitor", "jinja2.nodes.NSRef", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_NSRef of FrameSymbolVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "node", "store_as_param", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.FrameSymbolVisitor.visit_Name", "name": "visit_Name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "node", "store_as_param", "kwargs"], "arg_types": ["jinja2.idtracking.FrameSymbolVisitor", "jinja2.nodes.Name", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Name of FrameSymbolVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_OverlayScope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.FrameSymbolVisitor.visit_OverlayScope", "name": "visit_OverlayScope", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.FrameSymbolVisitor", "jinja2.nodes.OverlayScope", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_OverlayScope of FrameSymbolVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Scope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.FrameSymbolVisitor.visit_Scope", "name": "visit_Scope", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.FrameSymbolVisitor", "jinja2.nodes.Scope", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Scope of FrameSymbolVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_With": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.FrameSymbolVisitor.visit_With", "name": "visit_With", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.FrameSymbolVisitor", "jinja2.nodes.With", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_With of FrameSymbolVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.idtracking.FrameSymbolVisitor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.idtracking.FrameSymbolVisitor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NodeVisitor": {".class": "SymbolTableNode", "cross_ref": "jinja2.visitor.NodeVisitor", "kind": "Gdef"}, "RootVisitor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.visitor.NodeVisitor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.idtracking.RootVisitor", "name": "RootVisitor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.RootVisitor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jinja2.idtracking", "mro": ["jinja2.idtracking.RootVisitor", "jinja2.visitor.NodeVisitor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "symbols"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.RootVisitor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "symbols"], "arg_types": ["jinja2.idtracking.RootVisitor", "jinja2.idtracking.Symbols"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RootVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_simple_visit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.RootVisitor._simple_visit", "name": "_simple_visit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.RootVisitor", "jinja2.nodes.Node", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_simple_visit of RootVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generic_visit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "node", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.RootVisitor.generic_visit", "name": "generic_visit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "node", "args", "kwargs"], "arg_types": ["jinja2.idtracking.RootVisitor", "jinja2.nodes.Node", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generic_visit of RootVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sym_visitor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.idtracking.RootVisitor.sym_visitor", "name": "sym_visitor", "type": "jinja2.idtracking.FrameSymbolVisitor"}}, "visit_AssignBlock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.RootVisitor.visit_AssignBlock", "name": "visit_AssignBlock", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.RootVisitor", "jinja2.nodes.AssignBlock", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_AssignBlock of RootVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Block": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.idtracking.RootVisitor.visit_Block", "name": "visit_Block", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.RootVisitor", "jinja2.nodes.Node", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_CallBlock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.RootVisitor.visit_CallBlock", "name": "visit_CallBlock", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.RootVisitor", "jinja2.nodes.CallBlock", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_CallBlock of RootVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_FilterBlock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.idtracking.RootVisitor.visit_FilterBlock", "name": "visit_FilterBlock", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.RootVisitor", "jinja2.nodes.Node", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_For": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "node", "for_branch", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.RootVisitor.visit_For", "name": "visit_For", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "node", "for_branch", "kwargs"], "arg_types": ["jinja2.idtracking.RootVisitor", "jinja2.nodes.For", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_For of RootVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_If": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.idtracking.RootVisitor.visit_If", "name": "visit_If", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.RootVisitor", "jinja2.nodes.Node", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Macro": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.idtracking.RootVisitor.visit_Macro", "name": "visit_<PERSON>ro", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.RootVisitor", "jinja2.nodes.Node", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_OverlayScope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.RootVisitor.visit_OverlayScope", "name": "visit_OverlayScope", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.RootVisitor", "jinja2.nodes.OverlayScope", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_OverlayScope of RootVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Scope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.idtracking.RootVisitor.visit_Scope", "name": "visit_Scope", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.RootVisitor", "jinja2.nodes.Node", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_ScopedEvalContextModifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.idtracking.RootVisitor.visit_ScopedEvalContextModifier", "name": "visit_ScopedEvalContextModifier", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.RootVisitor", "jinja2.nodes.Node", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.idtracking.RootVisitor.visit_Template", "name": "visit_Template", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.RootVisitor", "jinja2.nodes.Node", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_With": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.RootVisitor.visit_With", "name": "visit_With", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.RootVisitor", "jinja2.nodes.With", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_With of RootVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.idtracking.RootVisitor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.idtracking.RootVisitor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Symbols": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.idtracking.Symbols", "name": "Symbols", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.Symbols", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jinja2.idtracking", "mro": ["jinja2.idtracking.Symbols", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "parent", "level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.Symbols.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "parent", "level"], "arg_types": ["jinja2.idtracking.Symbols", {".class": "UnionType", "items": ["jinja2.idtracking.Symbols", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Symbols", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_define_ref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "load"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.Symbols._define_ref", "name": "_define_ref", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "load"], "arg_types": ["jinja2.idtracking.Symbols", "builtins.str", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_define_ref of Symbols", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "analyze_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.Symbols.analyze_node", "name": "analyze_node", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "node", "kwargs"], "arg_types": ["jinja2.idtracking.Symbols", "jinja2.nodes.Node", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "analyze_node of Symbols", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "branch_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "branch_symbols"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.Symbols.branch_update", "name": "branch_update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "branch_symbols"], "arg_types": ["jinja2.idtracking.Symbols", {".class": "Instance", "args": ["jinja2.idtracking.Symbols"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "branch_update of Symbols", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.Symbols.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.idtracking.Symbols"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of Symbols", "ret_type": "jinja2.idtracking.Symbols", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "declare_parameter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.Symbols.declare_parameter", "name": "declare_parameter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["jinja2.idtracking.Symbols", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "declare_parameter of Symbols", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dump_param_targets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.Symbols.dump_param_targets", "name": "dump_param_targets", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.idtracking.Symbols"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dump_param_targets of Symbols", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dump_stores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.Symbols.dump_stores", "name": "dump_stores", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.idtracking.Symbols"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dump_stores of Symbols", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "target"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.Symbols.find_load", "name": "find_load", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "target"], "arg_types": ["jinja2.idtracking.Symbols", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_load of Symbols", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_ref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.Symbols.find_ref", "name": "find_ref", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["jinja2.idtracking.Symbols", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_ref of Symbols", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "level": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.idtracking.Symbols.level", "name": "level", "type": "builtins.int"}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.Symbols.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["jinja2.idtracking.Symbols", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of Symbols", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.idtracking.Symbols.loads", "name": "loads", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "parent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.idtracking.Symbols.parent", "name": "parent", "type": {".class": "UnionType", "items": ["jinja2.idtracking.Symbols", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.Symbols.ref", "name": "ref", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["jinja2.idtracking.Symbols", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ref of Symbols", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "refs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.idtracking.Symbols.refs", "name": "refs", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "store": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.Symbols.store", "name": "store", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["jinja2.idtracking.Symbols", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "store of Symbols", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stores": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.idtracking.Symbols.stores", "name": "stores", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.idtracking.Symbols.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.idtracking.Symbols", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VAR_LOAD_ALIAS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "jinja2.idtracking.VAR_LOAD_ALIAS", "name": "VAR_LOAD_ALIAS", "type": "builtins.str"}}, "VAR_LOAD_PARAMETER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "jinja2.idtracking.VAR_LOAD_PARAMETER", "name": "VAR_LOAD_PARAMETER", "type": "builtins.str"}}, "VAR_LOAD_RESOLVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "jinja2.idtracking.VAR_LOAD_RESOLVE", "name": "VAR_LOAD_RESOLVE", "type": "builtins.str"}}, "VAR_LOAD_UNDEFINED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "jinja2.idtracking.VAR_LOAD_UNDEFINED", "name": "VAR_LOAD_UNDEFINED", "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.idtracking.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.idtracking.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.idtracking.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.idtracking.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.idtracking.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.idtracking.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "find_symbols": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["nodes", "parent_symbols"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.find_symbols", "name": "find_symbols", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["nodes", "parent_symbols"], "arg_types": [{".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["jinja2.idtracking.Symbols", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_symbols", "ret_type": "jinja2.idtracking.Symbols", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nodes": {".class": "SymbolTableNode", "cross_ref": "jinja2.nodes", "kind": "Gdef"}, "symbols_for_node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["node", "parent_symbols"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.idtracking.symbols_for_node", "name": "symbols_for_node", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["node", "parent_symbols"], "arg_types": ["jinja2.nodes.Node", {".class": "UnionType", "items": ["jinja2.idtracking.Symbols", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "symbols_for_node", "ret_type": "jinja2.idtracking.Symbols", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\jinja2\\idtracking.py"}