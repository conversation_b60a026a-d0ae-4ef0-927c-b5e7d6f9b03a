{"data_mtime": 1754286446, "dep_lines": [25, 21, 22, 23, 26, 18, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["kornia.augmentation.base", "torch.nn", "torch.autograd", "torch.distributions", "kornia.core", "typing", "torch", "builtins", "string", "torch.distributed._functional_collectives", "types", "itertools", "contextlib", "traceback", "torch.distributed", "html", "operator", "os", "re", "sys", "multiprocessing.reduction", "collections", "warnings", "pprint", "inspect", "copy", "math", "uuid", "_frozen_importlib", "abc", "kornia.augmentation.random_generator", "kornia.augmentation.random_generator.base", "kornia.core.mixin", "kornia.core.mixin.onnx", "kornia.core.module", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.autograd.function", "torch.distributions.be<PERSON>i", "torch.distributions.distribution", "torch.distributions.exp_family", "torch.distributions.<PERSON>_be<PERSON><PERSON>i", "torch.distributions.transformed_distribution", "torch.nn.modules", "torch.nn.modules.module", "torch.nn.parameter"], "hash": "ee35d41c8d74f8b055832c008f806e1545280e85", "id": "kornia.augmentation.auto.operations.base", "ignore_all": true, "interface_hash": "db44b8ed6b2d633be4676d44fa52497dc3304bef", "mtime": 1740555645, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\kornia\\augmentation\\auto\\operations\\base.py", "plugin_data": null, "size": 8153, "suppressed": [], "version_id": "1.15.0"}