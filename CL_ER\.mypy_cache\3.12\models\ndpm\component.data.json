{".class": "MypyFile", "_fullname": "models.ndpm.component", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "Component": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["collect_nll", 1], ["nll", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "models.ndpm.component.Component", "name": "Component", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "fallback_to_any"], "fullname": "models.ndpm.component.Component", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "models.ndpm.component", "mro": ["models.ndpm.component.Component", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "params", "experts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.ndpm.component.Component.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "params", "experts"], "arg_types": ["models.ndpm.component.Component", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Component", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_clip_grad_norm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "max_norm", "norm_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.ndpm.component.Component._clip_grad_norm", "name": "_clip_grad_norm", "type": null}}, "_clip_grad_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "clip_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.ndpm.component.Component._clip_grad_value", "name": "_clip_grad_value", "type": null}}, "build_lr_scheduler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["lr_config", "optimizer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "models.ndpm.component.Component.build_lr_scheduler", "name": "build_lr_scheduler", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "models.ndpm.component.Component.build_lr_scheduler", "name": "build_lr_scheduler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["lr_config", "optimizer"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_lr_scheduler of Component", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "build_optimizer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["optim_config", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "models.ndpm.component.Component.build_optimizer", "name": "build_optimizer", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "models.ndpm.component.Component.build_optimizer", "name": "build_optimizer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["optim_config", "params"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_optimizer of Component", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "clip_grad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.ndpm.component.Component.clip_grad", "name": "clip_grad", "type": null}}, "collect_nll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "x", "y", "step"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "models.ndpm.component.Component.collect_nll", "name": "collect_nll", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "models.ndpm.component.Component.collect_nll", "name": "collect_nll", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "x", "y", "step"], "arg_types": ["models.ndpm.component.Component", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collect_nll of Component", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "experts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.ndpm.component.Component.experts", "name": "experts", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "lr_scheduler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.ndpm.component.Component.lr_scheduler", "name": "lr_scheduler", "type": "builtins._NotImplementedType"}}, "nll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "x", "y", "step"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "models.ndpm.component.Component.nll", "name": "nll", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "models.ndpm.component.Component.nll", "name": "nll", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "x", "y", "step"], "arg_types": ["models.ndpm.component.Component", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nll of Component", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "optimizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.ndpm.component.Component.optimizer", "name": "optimizer", "type": "builtins._NotImplementedType"}}, "params": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "models.ndpm.component.Component.params", "name": "params", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "weight_decay_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.ndpm.component.Component.weight_decay_loss", "name": "weight_decay_loss", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "models.ndpm.component.Component.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "models.ndpm.component.Component", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ComponentD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["nll", 1]], "alt_promote": null, "bases": ["models.ndpm.component.Component", "abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "models.ndpm.component.ComponentD", "name": "ComponentD", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "fallback_to_any"], "fullname": "models.ndpm.component.ComponentD", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "models.ndpm.component", "mro": ["models.ndpm.component.ComponentD", "models.ndpm.component.Component", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "collect_forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.ndpm.component.ComponentD.collect_forward", "name": "collect_forward", "type": null}}, "collect_nll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "x", "y", "step"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.ndpm.component.ComponentD.collect_nll", "name": "collect_nll", "type": null}}, "setup_optimizer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.ndpm.component.ComponentD.setup_optimizer", "name": "setup_optimizer", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "models.ndpm.component.ComponentD.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "models.ndpm.component.ComponentD", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ComponentG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["nll", 1]], "alt_promote": null, "bases": ["models.ndpm.component.Component", "abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "models.ndpm.component.ComponentG", "name": "ComponentG", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "fallback_to_any"], "fullname": "models.ndpm.component.ComponentG", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "models.ndpm.component", "mro": ["models.ndpm.component.ComponentG", "models.ndpm.component.Component", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "collect_nll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "x", "y", "step"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.ndpm.component.ComponentG.collect_nll", "name": "collect_nll", "type": null}}, "setup_optimizer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "models.ndpm.component.ComponentG.setup_optimizer", "name": "setup_optimizer", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "models.ndpm.component.ComponentG.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "models.ndpm.component.ComponentG", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MODELS_NDPM_CLASSIFIER_CLS_NF_BASE": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_CLASSIFIER_CLS_NF_BASE", "kind": "Gdef"}, "MODELS_NDPM_CLASSIFIER_CLS_NF_EXT": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_CLASSIFIER_CLS_NF_EXT", "kind": "Gdef"}, "MODELS_NDPM_CLASSIFIER_NORM_LAYER": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_CLASSIFIER_NORM_LAYER", "kind": "Gdef"}, "MODELS_NDPM_CLASSIFIER_NUM_BLOCKS": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_CLASSIFIER_NUM_BLOCKS", "kind": "Gdef"}, "MODELS_NDPM_COMPONENT_CLIP_GRAD": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_COMPONENT_CLIP_GRAD", "kind": "Gdef"}, "MODELS_NDPM_COMPONENT_LR_SCHEDULER_D": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_COMPONENT_LR_SCHEDULER_D", "kind": "Gdef"}, "MODELS_NDPM_COMPONENT_LR_SCHEDULER_G": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_COMPONENT_LR_SCHEDULER_G", "kind": "Gdef"}, "MODELS_NDPM_NDPM_DISABLE_D": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_NDPM_DISABLE_D", "kind": "Gdef"}, "MODELS_NDPM_NDPM_IMPLICIT_LR_DECAY": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_NDPM_IMPLICIT_LR_DECAY", "kind": "Gdef"}, "MODELS_NDPM_NDPM_SEND_TO_STM_ALWAYS": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_NDPM_SEND_TO_STM_ALWAYS", "kind": "Gdef"}, "MODELS_NDPM_NDPM_SLEEP_BATCH_SIZE": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_NDPM_SLEEP_BATCH_SIZE", "kind": "Gdef"}, "MODELS_NDPM_NDPM_SLEEP_NUM_WORKERS": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_NDPM_SLEEP_NUM_WORKERS", "kind": "Gdef"}, "MODELS_NDPM_NDPM_SLEEP_SLEEP_VAL_SIZE": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_NDPM_SLEEP_SLEEP_VAL_SIZE", "kind": "Gdef"}, "MODELS_NDPM_NDPM_SLEEP_STEP_D": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_NDPM_SLEEP_STEP_D", "kind": "Gdef"}, "MODELS_NDPM_NDPM_SLEEP_STEP_G": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_NDPM_SLEEP_STEP_G", "kind": "Gdef"}, "MODELS_NDPM_NDPM_SLEEP_SUMMARY_STEP": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_NDPM_SLEEP_SUMMARY_STEP", "kind": "Gdef"}, "MODELS_NDPM_NDPM_WEIGHT_DECAY": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_NDPM_WEIGHT_DECAY", "kind": "Gdef"}, "MODELS_NDPM_VAE_LEARN_X_LOG_VAR": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_VAE_LEARN_X_LOG_VAR", "kind": "Gdef"}, "MODELS_NDPM_VAE_NF_EXT": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_VAE_NF_EXT", "kind": "Gdef"}, "MODELS_NDPM_VAE_PRECURSOR_CONDITIONED_DECODER": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_VAE_PRECURSOR_CONDITIONED_DECODER", "kind": "Gdef"}, "MODELS_NDPM_VAE_RECON_LOSS": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_VAE_RECON_LOSS", "kind": "Gdef"}, "MODELS_NDPM_VAE_X_LOG_VAR_PARAM": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_VAE_X_LOG_VAR_PARAM", "kind": "Gdef"}, "MODELS_NDPM_VAE_Z_DIM": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_VAE_Z_DIM", "kind": "Gdef"}, "MODELS_NDPM_VAE_Z_SAMPLES": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODELS_NDPM_VAE_Z_SAMPLES", "kind": "Gdef"}, "MODLES_NDPM_VAE_NF_BASE": {".class": "SymbolTableNode", "cross_ref": "utils.global_vars.MODLES_NDPM_VAE_NF_BASE", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "models.ndpm.component.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "models.ndpm.component.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "models.ndpm.component.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "models.ndpm.component.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "models.ndpm.component.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "models.ndpm.component.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "maybe_cuda": {".class": "SymbolTableNode", "cross_ref": "utils.utils.maybe_cuda", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "models.ndpm.component.nn", "name": "nn", "type": {".class": "AnyType", "missing_import_name": "models.ndpm.component.nn", "source_any": null, "type_of_any": 3}}}, "torch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "models.ndpm.component.torch", "name": "torch", "type": {".class": "AnyType", "missing_import_name": "models.ndpm.component.torch", "source_any": null, "type_of_any": 3}}}}, "path": "D:\\continual_learning\\CL_ER\\models\\ndpm\\component.py"}