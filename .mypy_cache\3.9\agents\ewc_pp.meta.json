{"data_mtime": 1751962524, "dep_lines": [5, 2, 3, 4, 5, 6, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30], "dependencies": ["torch.utils.data", "agents.base", "continuum.data_utils", "utils.setup_elements", "torch.utils", "utils.utils", "torch", "builtins", "inspect", "re", "torch.distributed", "os", "itertools", "contextlib", "uuid", "sys", "typing", "collections", "torch.distributed._functional_collectives", "string", "multiprocessing.reduction", "pprint", "math", "torch.nn", "html", "traceback", "copy", "operator", "types", "warnings", "_frozen_importlib", "abc", "torch.nn.modules", "torch.nn.modules.module"], "hash": "e5a8577e10ace91627c9436f0e50cae96da980dd", "id": "agents.ewc_pp", "ignore_all": true, "interface_hash": "bcc56e0efe9f6bb68e479db88220b40225ecfdf1", "mtime": 1751894900, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\agents\\ewc_pp.py", "plugin_data": null, "size": 7608, "suppressed": [], "version_id": "1.15.0"}