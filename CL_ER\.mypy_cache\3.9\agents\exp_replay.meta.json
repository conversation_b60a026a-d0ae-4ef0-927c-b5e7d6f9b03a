{"data_mtime": 1754286450, "dep_lines": [2, 10, 21, 2, 5, 6, 7, 8, 10, 11, 12, 1, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 20, 5, 5, 5, 5, 10, 5, 5, 10, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.utils.data", "torch.nn.functional", "utils.buffer.buffer", "torch.utils", "agents.base", "continuum.data_utils", "utils.setup_elements", "utils.utils", "torch.nn", "UDDA.gdd", "UDDA.lsd", "torch", "Contrastive_loss", "builtins", "string", "torch.distributed._functional_collectives", "types", "itertools", "contextlib", "traceback", "torch.distributed", "html", "operator", "os", "re", "sys", "typing", "multiprocessing.reduction", "collections", "warnings", "pprint", "inspect", "copy", "math", "uuid", "_frozen_importlib", "abc", "torch.nn.modules", "torch.nn.modules.module", "utils", "utils.buffer"], "hash": "17f96b5455b5a986f91758886a30a2662f12377f", "id": "agents.exp_replay", "ignore_all": true, "interface_hash": "9188adccd5873dae9db4d3bb4f73e075e348becf", "mtime": 1753063354, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\agents\\exp_replay.py", "plugin_data": null, "size": 5142, "suppressed": [], "version_id": "1.15.0"}