{"data_mtime": 1754286791, "dep_lines": [3, 904, 2, 5, 1, 4, 6, 759, 902, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 886], "dep_prios": [10, 20, 10, 5, 10, 10, 10, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["torch.nn.functional", "torch.utils.data", "torch.nn", "agents.exp_replay", "torch", "numpy", "copy", "traceback", "pickle", "builtins", "_frozen_importlib", "_typeshed", "abc", "agents.base", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.module", "torch.types", "typing"], "hash": "870e21a84a7bef4320628efd9ced482fa72a9032", "id": "agents.advanced_unlearning", "ignore_all": true, "interface_hash": "5f8f2e333dfa2abb44988d5d4d5669e39f889173", "mtime": 1753096807, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\agents\\advanced_unlearning.py", "plugin_data": null, "size": 42270, "suppressed": ["sklearn.metrics"], "version_id": "1.15.0"}