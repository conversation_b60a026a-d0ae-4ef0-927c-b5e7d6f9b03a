{"data_mtime": 1754288103, "dep_lines": [3, 7, 8, 2, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.utils.data", "models.ndpm.expert", "models.ndpm.priors", "torch.nn", "utils.utils", "utils.global_vars", "torch", "builtins", "_frozen_importlib", "abc", "torch._C", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "torch.utils", "torch.utils._contextlib", "torch.utils.data.dataset", "typing", "utils"], "hash": "8cfe7c2c6aa44d125904f3700328f1d3233c8bad", "id": "models.ndpm.ndpm", "ignore_all": true, "interface_hash": "ef80b0b26622903933777c6eb6c918a169dc390b", "mtime": 1751894924, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\models\\ndpm\\ndpm.py", "plugin_data": null, "size": 7513, "suppressed": [], "version_id": "1.15.0"}