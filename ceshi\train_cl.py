import argparse
import numpy as np
import torch
import torch.nn.functional as F
import torch.nn as nn
from tqdm import tqdm
from torch import optim
from data_loader import get_subject_loader
from model import OCDNetWrapper
from config import config
from buffer import Buffer
from ocdnet import OCDNet

def evaluate(model, subject_ids, session_ids=[1, 2, 3]):
    """评估模型在指定受试者各会话的表现"""
    model.eval()
    accuracies = {sub_id: [] for sub_id in subject_ids}
    with torch.no_grad():
        for sub_id in subject_ids:
            for sess_id in session_ids:
                # 加载该会话的测试数据
                test_loader = get_subject_loader(sub_id, session_ids=[sess_id], data_type='test')
                correct = 0
                total = 0
                for x, y in test_loader:
                    x = x.to(config.device)
                    outputs = model(x, sub_id)
                    pred = outputs.argmax(dim=1).to(config.device)
                    correct += (pred == y).sum().item()
                    total += y.size(0)
                acc = correct / total if total > 0 else 0.0
                accuracies[sub_id].append(acc)
                print(f"Subject {sub_id} | Session {sess_id} | Test Acc: {acc:.2%}")
    return accuracies


def train_task(model, buffer, subject_id, session_ids=[1, 2, 3]):
    print(f"[DEBUG] 数据设备确认:")

    print(f"模型设备: {next(model.parameters()).device}")

    if not buffer.is_empty():
        buffered_data = buffer.get_data(1)

        print(f"缓存数据设备: {buffered_data[0].device}")

    # 加载该受试者所有会话的训练数据
    train_loader = get_subject_loader(subject_id, session_ids=session_ids, data_type='train')
    """训练单个受试者（合并所有会话数据）"""
    optimizer = optim.AdamW(
        [
            {'params': model.feature_extractor.parameters(), 'weight_decay': 1e-4},
            {'params': model.heads[str(subject_id)].parameters()}
        ],
        lr=config.lr
    )
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
        optimizer,
        T_max=config.epochs_per_subject * len(train_loader),  # 每个subject的完整训练周期
        eta_min=config.min_lr
    )
    # 每个batch后更新学习率
    optimizer.step()
    scheduler.step()
    # 加载该受试者所有会话的训练数据
    # train_loader = get_subject_loader(subject_id, session_ids=session_ids, data_type='train')

    # train_cl.py 训练循环中插入早停逻辑
    best_acc = 0.0
    patience = 5
    patience_counter = 0
    for epoch in range(config.epochs_per_subject):
        model.train()
        total_loss = 0.0
        progress_bar = tqdm(train_loader, desc=f"Training Subject {subject_id} | Epoch {epoch + 1}")

        for x, y in progress_bar:
            x, y = x.to(config.device), y.to(config.device)

            # 当前任务的损失
            outputs = model(x, subject_id)
            loss = F.cross_entropy(outputs, y)

            # 从buffer中回放历史数据的损失
            # if not buffer.is_empty():
            #     batch_size_half = config.batch_size // 2
            #     buffered_data = buffer.get_data(batch_size_half)
            #     x_buffer = buffered_data[0].to(config.device)
            #     y_buffer = buffered_data[1].to(config.device)
            #     t_buffer = buffered_data[3].tolist()  # 包含 task_labels (subject_id)

                # buffer_loss = 0.0
                # for x_b, y_b, t_b in zip(x_buffer, y_buffer, t_buffer):
                #     outputs_b = model(x_b.unsqueeze(0), t_b)
                #     buffer_loss += F.cross_entropy(outputs_b, y_b.unsqueeze(0))
                #
                # loss += buffer_loss / len(x_buffer)
            # OCDNet特有的对比蒸馏损失
            if not buffer.is_empty():
                buffered_data = buffer.get_data(config.batch_size // 2)
                x_b, y_b, logits_b, t_b = buffered_data
                loss_dis = model.calculate_distillation_loss(x_b, logits_b, t_b)
                loss += loss_dis * config.distill_weight
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            # for name, param in model.named_parameters():
            #
            #     if param.grad is None:
            #
            #         print(f"[梯度异常] {name} 无梯度")
            #
            #     else:
            #
            #         print(f"[梯度正常] {name} | 梯度均值: {param.grad.abs().mean().item():.6f}")

            optimizer.step()
            total_loss += loss.item()
            progress_bar.set_postfix({'loss': f"{loss.item():.4f}"})
        # 每个epoch后验证准确率
        # val_acc = evaluate(model, [subject_id], session_ids=[1])  # 用第一个session做验证集
        # if val_acc > best_acc:
        #     best_acc = val_acc
        #     patience_counter = 0
        # else:
        #     patience_counter += 1
        #     if patience_counter >= patience:
        #         print(f"早停触发于Epoch {epoch + 1}")
        #         break
        print(f"Epoch {epoch + 1} | Avg Loss: {total_loss / len(train_loader):.4f}")
        # 更新进度条描述信息
        progress_bar.set_description(
            f"Training Subject {subject_id} | Epoch {epoch + 1} | Avg Loss: {total_loss / len(train_loader):.4f}")
        progress_bar.refresh()
    # 将部分当前任务数据存入缓冲区
    model.eval()
    save_samples = []
    with torch.no_grad():
        for x, y in train_loader:
            x = x.to(config.device)
            logits = model(x, subject_id).to(config.device)
            # 概率采样存入
            mask = torch.rand(len(x)) < config.store_prob
            save_samples.append((
                x[mask],
                y[mask],
                logits[mask],
                torch.full((mask.sum(),), subject_id,device=config.device)
            ))

    # 调用Buffer的add_data方法
    for batch in save_samples:
        buffer.add_data(
            examples=batch[0],
            labels=batch[1],
            logits=batch[2],
            task_labels=batch[3]
        )
# def train_task(model, buffer, subject_id):
    # ...[原有代码]...
    if not buffer.is_empty():
        # [!++ 新增保护性代码 ++!]
        min_buffer_size = 2  # 必须确保至少有2个样本才能执行回放
        if buffer.num_seen_examples < min_buffer_size:
            print("[WARN] 缓冲样本不足，跳过回放")
            buffer_loss = 0.0
        else:
            batch_size_half = max(config.batch_size // 2, min_buffer_size)
            buffered_data = buffer.get_data(batch_size_half)
            x_buffer = buffered_data[0].to(config.device)
            y_buffer = buffered_data[1].to(config.device)
            t_buffer = buffered_data[3].tolist()

            buffer_loss = 0.0
            # [!++ 批量处理而非单个样本 ++!]
            for t_id in set(t_buffer):  # 按任务批量处理
                mask = torch.tensor([t == t_id for t in t_buffer], dtype=torch.bool)
                x_b_subset = x_buffer[mask]
                y_b_subset = y_buffer[mask]
                if len(x_b_subset) == 0:
                    continue
                outputs_b = model(x_b_subset, t_id)
                buffer_loss += F.cross_entropy(outputs_b, y_b_subset)  # [!关键改动!]

def continual_learning():
    """持续学习主流程：按顺序训练各受试者"""
    # 初始化模型和缓冲
    model = OCDNetWrapper().to(config.device)
    buffer = Buffer(
        buffer_size=config.replay_buffer_size,
        device=config.device,
        n_tasks=config.n_subjects,
        mode='reservoir'
    )
    buffer.empty()
    # 记录已学习的受试者
    learned_subjects = []

    # 依次训练每个受试者
    for subject_id in range(1, config.n_subjects + 1):
        learned_subjects.append(subject_id)
        print(f"\n=== Training Subject {subject_id} ===")

        # 添加该受试者的分类头
        model.add_subject_head(subject_id)

        # 训练当前受试者
        train_task(model, buffer, subject_id)

        # 评估所有已学习受试者
        print("\n[Evaluation after Training Subject {}]".format(subject_id))
        evaluate(model, learned_subjects)

    return model, buffer


# def unlearning(model, buffer, subject_id):
#     """遗忘指定的受试者"""
#     print(f"\n=== Unlearning Subject {subject_id} ===")
#     # 1. 从缓冲区删除与该受试者相关的所有数据
#     buffer.remove_task(subject_id - 1)  # Buffer内部task_id从0开始
#
#     # 2. 重置该受试者的分类头参数
#     # new_head = nn.Linear(64,3).to(config.device)
#     # model.heads[str(subject_id)].reset_parameters()
#     #
#     # # 3. 微调共享特征层（使用剩余数据）
#     # if not buffer.is_empty():
#     #     optimizer = optim.Adam(model.feature_extractor.parameters(), lr=config.lr / 10)
#     if str(subject_id) in model.heads:
#         # 方法1: 完全替换为新头
#         model.heads[str(subject_id)] = nn.Linear(64, 3).to(config.device)
#         # 或方法2: 权重随机化
#         with torch.no_grad():
#             for param in model.heads[str(subject_id)].parameters():
#                 param.normal_(mean=0, std=0.01)  # 小范围随机初始化
#     else:
#         raise ValueError(f"无法找到受试者 {subject_id}对应的分类头")
#
#         # 微调共享特征提取器
#     if not buffer.is_empty():
#         optimizer = optim.Adam(model.feature_extractor.parameters(),
#                                lr=config.lr / 10,  # 更低学习率
#                                weight_decay=1e-5)  # 增强正则化 [!新增!]
#         for epoch in range(config.ul_epochs):
#             total_loss = 0.0
#             buffer_data = buffer.get_data(config.batch_size)
#             x_b, y_b, _, t_b = buffer_data
#             x_b, y_b = x_b.to(config.device), y_b.to(config.device)
#
#             model.train()
#             for x, y, t in zip(x_b, y_b, t_b):
#                 optimizer.zero_grad()
#                 out = model(x.unsqueeze(0), t.item())
#                 loss = F.cross_entropy(out, y.unsqueeze(0))
#                 loss.backward()
#                 optimizer.step()
#                 total_loss += loss.item()
#
#             print(f"Unlearning Epoch {epoch + 1} | Avg Loss: {total_loss / len(x_b):.4f}")
def unlearning(model, buffer, subject_id):
    print(f"\n=== 开始遗忘受试者 {subject_id} ===")

    # Step1: 从缓冲区移除目标数据 [!注意: task_labels从0开始!]
    buffer.remove_task(subject_id - 1)

    # Step2: 重置分类头 [!确保头存在!]
    if not hasattr(model.heads, str(subject_id)):
        raise KeyError(f"无法找到受试者 {subject_id} 的分类头")
    new_head = nn.Linear(64, 3).to(config.device)  # 新建头部替换旧参数
    model.heads[str(subject_id)] = new_head

    # Step3: 基于剩余数据进行微调 [!动态批处理!]
    if buffer.num_seen_examples < config.min_buffer_for_finetune:
        print(f"[WARNING] 缓冲不足{config.min_buffer_for_finetune}，无法微调")
        return

    optimizer = optim.AdamW(
        model.feature_extractor.parameters(),
        lr=config.lr / 10,
        weight_decay=1e-5  # 增强正则化防止过拟合
    )

    # 微调循环
    for epoch in range(config.ul_epochs):
        model.train()
        buffer_size = min(config.batch_size, buffer.num_seen_examples)
        buffered_data = buffer.get_data(buffer_size)

        # 空数据防护
        if buffered_data is None or len(buffered_data[0]) < 2:
            print(f"Epoch {epoch} 无效数据，跳过")
            continue

        x_batch, y_batch, _, t_batch = buffered_data
        # 设备转移
        x_batch = x_batch.to(config.device)
        y_batch = y_batch.to(config.device)

        # 前向与损失计算
        outputs = model(x_batch, subject_id)  # [!假设batch中全为其他任务数据!]
        loss = F.cross_entropy(outputs, y_batch)

        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        # 梯度裁剪防止爆炸 [!新增!]
        torch.nn.utils.clip_grad_norm_(model.feature_extractor.parameters(), 0.5)
        optimizer.step()

        print(f"遗忘微调 Epoch {epoch + 1} | Loss: {loss.item():.4f}")

    # 评估遗忘效果
    print("\n[After Unlearning Evaluation]")
    print("\n=== 遗忘后评估 (目标受试者应显著性能下降) ===")
    prev_acc = evaluate(model, [subject_id])  # 保存遗留准确率
    post_acc = evaluate(model, [subject_id])  # 遗忘后准确率
    print(f"受试者 {subject_id} 的平均分类准确率变化: {prev_acc:.1%} → {post_acc:.1%}")

    evaluate(model, [subject_id])


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Continual Learning on SEED3 Dataset')
    parser.add_argument('--forget_subject', type=int, default=None, help='Subject ID to unlearn')
    args = parser.parse_args()

    # 阶段1: 持续学习训练
    model, buffer = continual_learning()

    # 阶段2: 选择性遗忘（示例）
    if args.forget_subject is not None:
        unlearning(model, buffer, args.forget_subject)
    # parser = argparse.ArgumentParser(description='Continual Learning on SEED3 Dataset')
    # # 新增短参数名 -f 提高可用性 [!关键改动!]
    # parser.add_argument('-f', '--forget_subject', type=int, default=None,
    #                     help='需要遗忘的受试者ID (范围: 1~15)')
    # args = parser.parse_args()
    #
    # # 检查参数合法性 [!新增校验!]
    # if args.forget_subject is not None:
    #     assert 1 <= args.forget_subject <= config.n_subjects, \
    #         f"受试者ID须在1~{config.n_subjects}之间，当前输入：{args.forget_subject}"
    #
    # # 执行主流程
    # if args.forget_subject:
    #     # 初始化遗忘前的完整模型和缓冲 [!这里可能需要加载预训练模型状态!]
    #     pre_trained_model = torch.load("final_model.pth")  # 假设保存了完整训练后的模型
    #     model.load_state_dict(pre_trained_model)
    #     unlearning(model, buffer, args.forget_subject)  # 执行遗忘逻辑
    # else:
    #     model, buffer = continual_learning()  # 执行常规持续学习

    # 保存最终模型
    torch.save(model.state_dict(), "final_model.pth")
