{".class": "MypyFile", "_fullname": "agents.advanced_unlearning", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "EnhancedUnlearningAgent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["agents.exp_replay.ExperienceReplay"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent", "name": "EnhancedUnlearningAgent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "agents.advanced_unlearning", "mro": ["agents.advanced_unlearning.EnhancedUnlearningAgent", "agents.exp_replay.ExperienceReplay", "agents.base.ContinualLearner", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "optimizer", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent.__init__", "name": "__init__", "type": null}}, "_adversarial_unlearning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "forget_x", "forget_y", "retain_x", "retain_y", "full_teacher", "bad_teacher", "forget_task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent._adversarial_unlearning", "name": "_adversarial_unlearning", "type": null}}, "_create_bad_teacher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "forget_task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent._create_bad_teacher", "name": "_create_bad_teacher", "type": null}}, "_create_teacher_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent._create_teacher_model", "name": "_create_teacher_model", "type": null}}, "_dual_teacher_unlearning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "forget_x", "forget_y", "retain_x", "retain_y", "full_teacher", "bad_teacher", "forget_task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent._dual_teacher_unlearning", "name": "_dual_teacher_unlearning", "type": null}}, "_evaluate_on_test_loaders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "test_loaders"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent._evaluate_on_test_loaders", "name": "_evaluate_on_test_loaders", "type": null}}, "_generate_adversarial_examples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent._generate_adversarial_examples", "name": "_generate_adversarial_examples", "type": null}}, "_get_task_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent._get_task_data", "name": "_get_task_data", "type": null}}, "_load_test_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent._load_test_data", "name": "_load_test_data", "type": null}}, "_load_test_data_for_subject": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent._load_test_data_for_subject", "name": "_load_test_data_for_subject", "type": null}}, "_remove_task_from_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent._remove_task_from_buffer", "name": "_remove_task_from_buffer", "type": null}}, "adv_weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent.adv_weight", "name": "adv_weight", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "distill_weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent.distill_weight", "name": "distill_weight", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "epsilon": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent.epsilon", "name": "epsilon", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "evaluate_accuracy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent.evaluate_accuracy", "name": "evaluate_accuracy", "type": null}}, "evaluate_membership_inference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "forget_x", "forget_y", "retain_x", "retain_y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent.evaluate_membership_inference", "name": "evaluate_membership_inference", "type": null}}, "forget": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "forget_task_id", "subjects_to_keep", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent.forget", "name": "forget", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "forget_task_id", "subjects_to_keep", "mode"], "arg_types": ["agents.advanced_unlearning.EnhancedUnlearningAgent", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forget of EnhancedUnlearningAgent", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "kl_temperature": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent.kl_temperature", "name": "kl_temperature", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "model_cls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent.model_cls", "name": "model_cls", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "model_encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent.model_encoder", "name": "model_encoder", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "retain_weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent.retain_weight", "name": "retain_weight", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "unlearning_epochs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent.unlearning_epochs", "name": "unlearning_epochs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "agents.advanced_unlearning.EnhancedUnlearningAgent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "agents.advanced_unlearning.EnhancedUnlearningAgent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExperienceReplay": {".class": "SymbolTableNode", "cross_ref": "agents.exp_replay.ExperienceReplay", "kind": "Gdef"}, "F": {".class": "SymbolTableNode", "cross_ref": "torch.nn.functional", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.advanced_unlearning.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.advanced_unlearning.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.advanced_unlearning.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.advanced_unlearning.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.advanced_unlearning.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.advanced_unlearning.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "D:\\continual_learning\\CL_ER\\agents\\advanced_unlearning.py"}