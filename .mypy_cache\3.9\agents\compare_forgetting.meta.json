{"data_mtime": 1754286791, "dep_lines": [2, 4, 8, 112, 117, 122, 127, 137, 1, 3, 4, 6, 7, 9, 485, 1, 1, 1, 1, 1, 1, 1, 1, 10, 11, 5, 12], "dep_prios": [10, 10, 5, 20, 20, 20, 20, 20, 10, 10, 20, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 5, 5, 10, 10], "dependencies": ["torch.nn", "matplotlib.pyplot", "agents.exp_replay", "agents.selective_surgical", "agents.adversarial", "agents.advanced_unlearning", "agents.noise_injection", "agents.hybrid", "torch", "numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "os", "time", "copy", "traceback", "builtins", "_frozen_importlib", "_typeshed", "abc", "agents.base", "torch.nn.modules", "torch.nn.modules.module", "typing"], "hash": "424c4f91b4036ad6a17a91136e901afa27edd7d4", "id": "agents.compare_forgetting", "ignore_all": true, "interface_hash": "fda18cd3e3d2d2dcb71ef28126d56d01ac068669", "mtime": 1751966821, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\agents\\compare_forgetting.py", "plugin_data": null, "size": 28286, "suppressed": ["sklearn.metrics", "sklearn.manifold", "seaborn", "pandas"], "version_id": "1.15.0"}