{".class": "MypyFile", "_fullname": "continuum.dataset_scripts.SEED", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DatasetBase": {".class": "SymbolTableNode", "cross_ref": "continuum.dataset_scripts.dataset_base.DatasetBase", "kind": "Gdef"}, "SEED_process": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["continuum.dataset_scripts.dataset_base.DatasetBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "continuum.dataset_scripts.SEED.SEED_process", "name": "SEED_process", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "continuum.dataset_scripts.SEED.SEED_process", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "continuum.dataset_scripts.SEED", "mro": ["continuum.dataset_scripts.SEED.SEED_process", "continuum.dataset_scripts.dataset_base.DatasetBase", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "scenario", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.dataset_scripts.SEED.SEED_process.__init__", "name": "__init__", "type": null}}, "download_load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.dataset_scripts.SEED.SEED_process.download_load", "name": "download_load", "type": null}}, "new_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.dataset_scripts.SEED.SEED_process.new_run", "name": "new_run", "type": null}}, "new_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "cur_task", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.dataset_scripts.SEED.SEED_process.new_task", "name": "new_task", "type": null}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.dataset_scripts.SEED.SEED_process.setup", "name": "setup", "type": null}}, "train_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.dataset_scripts.SEED.SEED_process.train_data", "name": "train_data", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "train_label": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.dataset_scripts.SEED.SEED_process.train_label", "name": "train_label", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "train_set": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.dataset_scripts.SEED.SEED_process.train_set", "name": "train_set", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "continuum.dataset_scripts.SEED.SEED_process.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "continuum.dataset_scripts.SEED.SEED_process", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TEST_SPLIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "continuum.dataset_scripts.SEED.TEST_SPLIT", "name": "TEST_SPLIT", "type": "builtins.float"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.dataset_scripts.SEED.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.dataset_scripts.SEED.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.dataset_scripts.SEED.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.dataset_scripts.SEED.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.dataset_scripts.SEED.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.dataset_scripts.SEED.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "construct_ns_multiple_wrapper": {".class": "SymbolTableNode", "cross_ref": "continuum.non_stationary.construct_ns_multiple_wrapper", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "pickle": {".class": "SymbolTableNode", "cross_ref": "pickle", "kind": "Gdef"}, "shuffle_data": {".class": "SymbolTableNode", "cross_ref": "continuum.data_utils.shuffle_data", "kind": "Gdef"}, "test_ns": {".class": "SymbolTableNode", "cross_ref": "continuum.non_stationary.test_ns", "kind": "Gdef"}}, "path": "D:\\continual_learning\\CL_ER\\continuum\\dataset_scripts\\SEED.py"}