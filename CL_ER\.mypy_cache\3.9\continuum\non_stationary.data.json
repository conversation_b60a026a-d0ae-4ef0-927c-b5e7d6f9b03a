{".class": "MypyFile", "_fullname": "continuum.non_stationary", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Original": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "continuum.non_stationary.Original", "name": "Original", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "continuum.non_stationary.Original", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "continuum.non_stationary", "mro": ["continuum.non_stationary.Original", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "x", "y", "unroll", "color"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.non_stationary.Original.__init__", "name": "__init__", "type": null}}, "clip_minmax": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["l", "min_", "max_"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "continuum.non_stationary.Original.clip_minmax", "name": "clip_minmax", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "continuum.non_stationary.Original.clip_minmax", "name": "clip_minmax", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["l", "min_", "max_"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clip_minmax of Original", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.non_stationary.Original.create_output", "name": "create_output", "type": null}}, "get_dims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.non_stationary.Original.get_dims", "name": "get_dims", "type": null}}, "get_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.non_stationary.Original.get_name", "name": "get_name", "type": null}}, "next_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.non_stationary.Original.next_task", "name": "next_task", "type": null}}, "next_x": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.non_stationary.Original.next_x", "name": "next_x", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "next_y": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.non_stationary.Original.next_y", "name": "next_y", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "show_sample": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "num_plot"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.non_stationary.Original.show_sample", "name": "show_sample", "type": null}}, "unroll": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.non_stationary.Original.unroll", "name": "unroll", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "x": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.non_stationary.Original.x", "name": "x", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "y": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "continuum.non_stationary.Original.y", "name": "y", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "continuum.non_stationary.Original.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "continuum.non_stationary.Original", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.non_stationary.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.non_stationary.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.non_stationary.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.non_stationary.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.non_stationary.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "continuum.non_stationary.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "construct_ns_multiple": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["train_x_split", "train_y_split", "test_x_split", "test_y_split", "change_factors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.non_stationary.construct_ns_multiple", "name": "construct_ns_multiple", "type": null}}, "construct_ns_multiple_wrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["train_data", "train_label", "test_data", "est_label", "ns_factor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.non_stationary.construct_ns_multiple_wrapper", "name": "construct_ns_multiple_wrapper", "type": null}}, "construct_ns_single": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["train_x_split", "train_y_split", "test_x_split", "test_y_split", "change_factor", "ns_task", "plot"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.non_stationary.construct_ns_single", "name": "construct_ns_single", "type": null}}, "deepcopy": {".class": "SymbolTableNode", "cross_ref": "copy.deepcopy", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "plt": {".class": "SymbolTableNode", "cross_ref": "matplotlib.pyplot", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "test_ns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["x", "y", "ns_type", "change_factor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "continuum.non_stationary.test_ns", "name": "test_ns", "type": null}}, "train_val_test_split_ni": {".class": "SymbolTableNode", "cross_ref": "continuum.data_utils.train_val_test_split_ni", "kind": "Gdef"}}, "path": "D:\\continual_learning\\CL_ER\\continuum\\non_stationary.py"}