{"data_mtime": 1754286791, "dep_lines": [2, 14, 15, 16, 17, 18, 19, 20, 21, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 24, 25, 26, 27, 28, 29, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["continuum.dataset_scripts.SEED", "utils.buffer.random_retrieve", "utils.buffer.reservoir_update", "utils.buffer.mir_retrieve", "utils.buffer.gss_greedy_update", "utils.buffer.aser_retrieve", "utils.buffer.aser_update", "utils.buffer.sc_retrieve", "utils.buffer.mem_match", "agents.gdumb", "agents.exp_replay", "agents.agem", "agents.ewc_pp", "agents.cndpm", "agents.lwf", "agents.icarl", "agents.scr", "agents.prototype_anchored_er", "agents.PAFL_ER", "agents.APUMM_ER", "agents.PAMU_ER", "agents.adversarial", "agents.advanced_unlearning", "agents.selective_surgical", "agents.noise_injection", "agents.hybrid", "agents.compare_forgetting", "builtins", "_frozen_importlib", "abc", "agents", "agents.base", "continuum", "continuum.dataset_scripts", "continuum.dataset_scripts.dataset_base", "torch", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "typing", "utils.buffer"], "hash": "b6c5c02af43e2ba37546e1451ef0f2fabb53e6e0", "id": "utils.name_match", "ignore_all": true, "interface_hash": "498ecf4dd5eb683f6d578124ff65e74f653ea2ff", "mtime": 1751951584, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\utils\\name_match.py", "plugin_data": null, "size": 2176, "suppressed": [], "version_id": "1.15.0"}