{"data_mtime": 1754286351, "dep_lines": [8, 16, 17, 18, 5, 6, 7, 8, 10, 13, 1, 1, 1, 1, 1, 1, 1, 12], "dep_prios": [10, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["collections.abc", "jinja2.environment", "jinja2.exceptions", "jinja2.runtime", "operator", "types", "typing", "collections", "string", "markupsafe", "builtins", "_frozen_importlib", "_operator", "abc", "jinja2.bccache", "jinja2.ext", "jinja2.loaders"], "hash": "31db08ef1d3343cb2a1be643201cc3c452a74b06", "id": "jinja2.sandbox", "ignore_all": true, "interface_hash": "05959e68edb94a194d6bfce418626827ef803d54", "mtime": 1716993775, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\jinja2\\sandbox.py", "plugin_data": null, "size": 14616, "suppressed": ["_string"], "version_id": "1.15.0"}