{".class": "MypyFile", "_fullname": "agents.scr", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AverageMeter": {".class": "SymbolTableNode", "cross_ref": "utils.utils.AverageMeter", "kind": "Gdef"}, "Buffer": {".class": "SymbolTableNode", "cross_ref": "utils.buffer.buffer.Buffer", "kind": "Gdef"}, "ColorJitter": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation._2d.intensity.color_jitter.ColorJitter", "kind": "Gdef"}, "ContinualLearner": {".class": "SymbolTableNode", "cross_ref": "agents.base.ContinualLearner", "kind": "Gdef"}, "RandomGrayscale": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation._2d.intensity.grayscale.RandomGrayscale", "kind": "Gdef"}, "RandomHorizontalFlip": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation._2d.geometric.horizontal_flip.RandomHorizontalFlip", "kind": "Gdef"}, "RandomResizedCrop": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation._2d.geometric.resized_crop.RandomResizedCrop", "kind": "Gdef"}, "SupContrastReplay": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["agents.base.ContinualLearner"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "agents.scr.SupContrastReplay", "name": "SupContrastReplay", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "agents.scr.SupContrastReplay", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "agents.scr", "mro": ["agents.scr.SupContrastReplay", "agents.base.ContinualLearner", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "opt", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.scr.SupContrastReplay.__init__", "name": "__init__", "type": null}}, "buffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.scr.SupContrastReplay.buffer", "name": "buffer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "eps_mem_batch": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.scr.SupContrastReplay.eps_mem_batch", "name": "eps_mem_batch", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "mem_iters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.scr.SupContrastReplay.mem_iters", "name": "mem_iters", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "mem_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.scr.SupContrastReplay.mem_size", "name": "mem_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "train_learner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x_train", "y_train"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.scr.SupContrastReplay.train_learner", "name": "train_learner", "type": null}}, "transform": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.scr.SupContrastReplay.transform", "name": "transform", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "agents.scr.SupContrastReplay.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "agents.scr.SupContrastReplay", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.scr.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.scr.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.scr.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.scr.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.scr.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.scr.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "data": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data", "kind": "Gdef"}, "dataset_transform": {".class": "SymbolTableNode", "cross_ref": "continuum.data_utils.dataset_transform", "kind": "Gdef"}, "input_size_match": {".class": "SymbolTableNode", "cross_ref": "utils.setup_elements.input_size_match", "kind": "Gdef"}, "maybe_cuda": {".class": "SymbolTableNode", "cross_ref": "utils.utils.maybe_cuda", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "transforms_match": {".class": "SymbolTableNode", "cross_ref": "utils.setup_elements.transforms_match", "kind": "Gdef"}}, "path": "D:\\continual_learning\\CL_ER\\agents\\scr.py"}