{"data_mtime": 1754286348, "dep_lines": [14, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 17, 18, 23, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 25, 25, 25, 5, 30, 30, 30, 30], "dependencies": ["filelock._error", "__future__", "contextlib", "logging", "os", "time", "warnings", "abc", "dataclasses", "threading", "typing", "weakref", "sys", "types", "typing_extensions", "builtins", "_frozen_importlib", "_thread", "_typeshed", "_warnings"], "hash": "52f9b32698d702f1d03cef2c78a6e3bc4f906416", "id": "filelock._api", "ignore_all": true, "interface_hash": "c473fd0034c5b33527b57dd024241d2c679a6823", "mtime": 1700591336, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\filelock\\_api.py", "plugin_data": null, "size": 11860, "suppressed": [], "version_id": "1.15.0"}