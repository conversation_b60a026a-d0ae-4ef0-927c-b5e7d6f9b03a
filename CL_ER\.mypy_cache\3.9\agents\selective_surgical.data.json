{".class": "MypyFile", "_fullname": "agents.selective_surgical", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ExperienceReplay": {".class": "SymbolTableNode", "cross_ref": "agents.exp_replay.ExperienceReplay", "kind": "Gdef"}, "F": {".class": "SymbolTableNode", "cross_ref": "torch.nn.functional", "kind": "Gdef"}, "SelectiveSurgicalAgent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["agents.exp_replay.ExperienceReplay"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "agents.selective_surgical.SelectiveSurgicalAgent", "name": "SelectiveSurgicalAgent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "agents.selective_surgical.SelectiveSurgicalAgent", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "agents.selective_surgical", "mro": ["agents.selective_surgical.SelectiveSurgicalAgent", "agents.exp_replay.ExperienceReplay", "agents.base.ContinualLearner", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "optimizer", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.selective_surgical.SelectiveSurgicalAgent.__init__", "name": "__init__", "type": null}}, "_fine_tune": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "retain_x", "retain_y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.selective_surgical.SelectiveSurgicalAgent._fine_tune", "name": "_fine_tune", "type": null}}, "_get_task_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.selective_surgical.SelectiveSurgicalAgent._get_task_data", "name": "_get_task_data", "type": null}}, "_identify_important_neurons": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "forget_x", "forget_y", "forget_task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.selective_surgical.SelectiveSurgicalAgent._identify_important_neurons", "name": "_identify_important_neurons", "type": null}}, "_remove_task_from_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.selective_surgical.SelectiveSurgicalAgent._remove_task_from_buffer", "name": "_remove_task_from_buffer", "type": null}}, "_reset_neurons": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "important_neurons"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.selective_surgical.SelectiveSurgicalAgent._reset_neurons", "name": "_reset_neurons", "type": null}}, "_selective_surgical_unlearning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "forget_x", "forget_y", "retain_x", "retain_y", "forget_task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.selective_surgical.SelectiveSurgicalAgent._selective_surgical_unlearning", "name": "_selective_surgical_unlearning", "type": null}}, "evaluate_accuracy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.selective_surgical.SelectiveSurgicalAgent.evaluate_accuracy", "name": "evaluate_accuracy", "type": null}}, "evaluate_membership_inference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "forget_x", "forget_y", "retain_x", "retain_y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.selective_surgical.SelectiveSurgicalAgent.evaluate_membership_inference", "name": "evaluate_membership_inference", "type": null}}, "forget": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "forget_task_id", "subjects_to_keep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.selective_surgical.SelectiveSurgicalAgent.forget", "name": "forget", "type": null}}, "model_cls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.selective_surgical.SelectiveSurgicalAgent.model_cls", "name": "model_cls", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "model_encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.selective_surgical.SelectiveSurgicalAgent.model_encoder", "name": "model_encoder", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "retention_ratio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.selective_surgical.SelectiveSurgicalAgent.retention_ratio", "name": "retention_ratio", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "unlearning_epochs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.selective_surgical.SelectiveSurgicalAgent.unlearning_epochs", "name": "unlearning_epochs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "agents.selective_surgical.SelectiveSurgicalAgent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "agents.selective_surgical.SelectiveSurgicalAgent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.selective_surgical.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.selective_surgical.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.selective_surgical.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.selective_surgical.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.selective_surgical.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.selective_surgical.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "D:\\continual_learning\\CL_ER\\agents\\selective_surgical.py"}