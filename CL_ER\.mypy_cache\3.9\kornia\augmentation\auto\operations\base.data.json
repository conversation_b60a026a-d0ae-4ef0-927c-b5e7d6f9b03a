{".class": "MypyFile", "_fullname": "kornia.augmentation.auto.operations.base", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Bernoulli": {".class": "SymbolTableNode", "cross_ref": "torch.distributions.bernoulli.Bernoulli", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Function": {".class": "SymbolTableNode", "cross_ref": "torch.autograd.function.Function", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Module": {".class": "SymbolTableNode", "cross_ref": "kornia.core._backend.Module", "kind": "Gdef"}, "OperationBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.base.OperationBase", "name": "OperationBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.base.OperationBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.base", "mro": ["kornia.augmentation.auto.operations.base.OperationBase", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "operation", "initial_magnitude", "temperature", "is_batch_operation", "magnitude_fn", "gradient_estimator", "symmetric_megnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.base.OperationBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "operation", "initial_magnitude", "temperature", "is_batch_operation", "magnitude_fn", "gradient_estimator", "symmetric_megnitude"], "arg_types": ["kornia.augmentation.auto.operations.base.OperationBase", "kornia.augmentation.base._AugmentationBase", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "torch.autograd.function.Function"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OperationBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_factor_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "kornia.augmentation.auto.operations.base.OperationBase._factor_name", "name": "_factor_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_gradient_estimator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "kornia.augmentation.auto.operations.base.OperationBase._gradient_estimator", "name": "_gradient_estimator", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "torch.autograd.function.Function"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_init_magnitude": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "initial_magnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.base.OperationBase._init_magnitude", "name": "_init_magnitude", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "initial_magnitude"], "arg_types": ["kornia.augmentation.auto.operations.base.OperationBase", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_magnitude of OperationBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_init_magnitude_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "magnitude_fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.base.OperationBase._init_magnitude_fn", "name": "_init_magnitude_fn", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "magnitude_fn"], "arg_types": ["kornia.augmentation.auto.operations.base.OperationBase", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_magnitude_fn of OperationBase", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_batch_operation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "kornia.augmentation.auto.operations.base.OperationBase._is_batch_operation", "name": "_is_batch_operation", "type": "builtins.bool"}}, "_magnitude": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "kornia.augmentation.auto.operations.base.OperationBase._magnitude", "name": "_magnitude", "type": {".class": "UnionType", "items": ["torch.nn.parameter.Parameter", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_magnitude_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "kornia.augmentation.auto.operations.base.OperationBase._magnitude_fn", "name": "_magnitude_fn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch._tensor.Tensor"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_probability": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "kornia.augmentation.auto.operations.base.OperationBase._probability", "name": "_probability", "type": "torch.nn.parameter.Parameter"}}, "_update_probability_gen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "relaxation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.base.OperationBase._update_probability_gen", "name": "_update_probability_gen", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "relaxation"], "arg_types": ["kornia.augmentation.auto.operations.base.OperationBase", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_probability_gen of OperationBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "eval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.base.OperationBase.eval", "name": "eval", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.base.T", "id": -1, "name": "T", "namespace": "kornia.augmentation.auto.operations.base.OperationBase.eval", "upper_bound": "kornia.augmentation.auto.operations.base.OperationBase", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eval of OperationBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.base.T", "id": -1, "name": "T", "namespace": "kornia.augmentation.auto.operations.base.OperationBase.eval", "upper_bound": "kornia.augmentation.auto.operations.base.OperationBase", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.base.T", "id": -1, "name": "T", "namespace": "kornia.augmentation.auto.operations.base.OperationBase.eval", "upper_bound": "kornia.augmentation.auto.operations.base.OperationBase", "values": [], "variance": 0}]}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "input", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.base.OperationBase.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "input", "params"], "arg_types": ["kornia.augmentation.auto.operations.base.OperationBase", "torch._tensor.Tensor", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of OperationBase", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "batch_shape", "mag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.base.OperationBase.forward_parameters", "name": "forward_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "batch_shape", "mag"], "arg_types": ["kornia.augmentation.auto.operations.base.OperationBase", "torch._<PERSON><PERSON>", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward_parameters of OperationBase", "ret_type": {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "magnitude": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "kornia.augmentation.auto.operations.base.OperationBase.magnitude", "name": "magnitude", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["kornia.augmentation.auto.operations.base.OperationBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "magnitude of OperationBase", "ret_type": {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "kornia.augmentation.auto.operations.base.OperationBase.magnitude", "name": "magnitude", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["kornia.augmentation.auto.operations.base.OperationBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "magnitude of OperationBase", "ret_type": {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "magnitude_range": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "kornia.augmentation.auto.operations.base.OperationBase.magnitude_range", "name": "magnitude_range", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "op": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "kornia.augmentation.auto.operations.base.OperationBase.op", "name": "op", "type": "kornia.augmentation.base._AugmentationBase"}}, "probability": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "kornia.augmentation.auto.operations.base.OperationBase.probability", "name": "probability", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["kornia.augmentation.auto.operations.base.OperationBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "probability of OperationBase", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "kornia.augmentation.auto.operations.base.OperationBase.probability", "name": "probability", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["kornia.augmentation.auto.operations.base.OperationBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "probability of OperationBase", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "probability_range": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "kornia.augmentation.auto.operations.base.OperationBase.probability_range", "name": "probability_range", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "symmetric_megnitude": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "kornia.augmentation.auto.operations.base.OperationBase.symmetric_megnitude", "name": "symmetric_megnitude", "type": "builtins.bool"}}, "train": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.base.OperationBase.train", "name": "train", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "mode"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.base.T", "id": -1, "name": "T", "namespace": "kornia.augmentation.auto.operations.base.OperationBase.train", "upper_bound": "kornia.augmentation.auto.operations.base.OperationBase", "values": [], "variance": 0}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "train of OperationBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.base.T", "id": -1, "name": "T", "namespace": "kornia.augmentation.auto.operations.base.OperationBase.train", "upper_bound": "kornia.augmentation.auto.operations.base.OperationBase", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.base.T", "id": -1, "name": "T", "namespace": "kornia.augmentation.auto.operations.base.OperationBase.train", "upper_bound": "kornia.augmentation.auto.operations.base.OperationBase", "values": [], "variance": 0}]}}}, "transform_matrix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "kornia.augmentation.auto.operations.base.OperationBase.transform_matrix", "name": "transform_matrix", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["kornia.augmentation.auto.operations.base.OperationBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transform_matrix of OperationBase", "ret_type": {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "kornia.augmentation.auto.operations.base.OperationBase.transform_matrix", "name": "transform_matrix", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["kornia.augmentation.auto.operations.base.OperationBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transform_matrix of OperationBase", "ret_type": {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.base.OperationBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.base.OperationBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RelaxedBernoulli": {".class": "SymbolTableNode", "cross_ref": "torch.distributions.relaxed_berno<PERSON><PERSON>.RelaxedBernoulli", "kind": "Gdef"}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.base.T", "name": "T", "upper_bound": "kornia.augmentation.auto.operations.base.OperationBase", "values": [], "variance": 0}}, "Tensor": {".class": "SymbolTableNode", "cross_ref": "kornia.core._backend.Tensor", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "_AugmentationBase": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.base._AugmentationBase", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.base.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.base.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\kornia\\augmentation\\auto\\operations\\base.py"}