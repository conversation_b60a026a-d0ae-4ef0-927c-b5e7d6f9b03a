{".class": "MypyFile", "_fullname": "kornia.augmentation.auto.autoaugment.ops", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AutoContrast": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.AutoContrast", "kind": "Gdef"}, "Brightness": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Brightness", "kind": "Gdef"}, "Contrast": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Contrast", "kind": "Gdef"}, "Equalize": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Equalize", "kind": "Gdef"}, "Invert": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Invert", "kind": "Gdef"}, "OperationBase": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.base.OperationBase", "kind": "Gdef"}, "Posterize": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Posterize", "kind": "Gdef"}, "Rotate": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Rotate", "kind": "Gdef"}, "Saturate": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Saturate", "kind": "Gdef"}, "Sharpness": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Sharpness", "kind": "Gdef"}, "ShearX": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.ShearX", "kind": "Gdef"}, "ShearY": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.ShearY", "kind": "Gdef"}, "Solarize": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.Solarize", "kind": "Gdef"}, "TranslateX": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.TranslateX", "kind": "Gdef"}, "TranslateY": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.ops.TranslateY", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.autoaugment.ops.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.autoaugment.ops.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.autoaugment.ops.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.autoaugment.ops.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.autoaugment.ops.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.autoaugment.ops.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "auto_contrast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["probability", "_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.autoaugment.ops.auto_contrast", "name": "auto_contrast", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["probability", "_"], "arg_types": ["builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "auto_contrast", "ret_type": "kornia.augmentation.auto.operations.base.OperationBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "brightness": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.autoaugment.ops.brightness", "name": "brightness", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "arg_types": ["builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "brightness", "ret_type": "kornia.augmentation.auto.operations.base.OperationBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "color": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.autoaugment.ops.color", "name": "color", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "arg_types": ["builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "color", "ret_type": "kornia.augmentation.auto.operations.base.OperationBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contrast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.autoaugment.ops.contrast", "name": "contrast", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "arg_types": ["builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "contrast", "ret_type": "kornia.augmentation.auto.operations.base.OperationBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "equalize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["probability", "_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.autoaugment.ops.equalize", "name": "equalize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["probability", "_"], "arg_types": ["builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "equalize", "ret_type": "kornia.augmentation.auto.operations.base.OperationBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "invert": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["probability", "_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.autoaugment.ops.invert", "name": "invert", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["probability", "_"], "arg_types": ["builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "invert", "ret_type": "kornia.augmentation.auto.operations.base.OperationBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "linspace": {".class": "SymbolTableNode", "cross_ref": "kornia.core._backend.linspace", "kind": "Gdef"}, "posterize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.autoaugment.ops.posterize", "name": "posterize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "arg_types": ["builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "posterize", "ret_type": "kornia.augmentation.auto.operations.base.OperationBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rotate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.autoaugment.ops.rotate", "name": "rotate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "arg_types": ["builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rotate", "ret_type": "kornia.augmentation.auto.operations.base.OperationBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sharpness": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.autoaugment.ops.sharpness", "name": "sharpness", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "arg_types": ["builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sharpness", "ret_type": "kornia.augmentation.auto.operations.base.OperationBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shear_x": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.autoaugment.ops.shear_x", "name": "shear_x", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "arg_types": ["builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shear_x", "ret_type": "kornia.augmentation.auto.operations.base.OperationBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shear_y": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.autoaugment.ops.shear_y", "name": "shear_y", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "arg_types": ["builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shear_y", "ret_type": "kornia.augmentation.auto.operations.base.OperationBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "solarize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.autoaugment.ops.solarize", "name": "solarize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "arg_types": ["builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "solarize", "ret_type": "kornia.augmentation.auto.operations.base.OperationBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "translate_x": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.autoaugment.ops.translate_x", "name": "translate_x", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "arg_types": ["builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "translate_x", "ret_type": "kornia.augmentation.auto.operations.base.OperationBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "translate_y": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.autoaugment.ops.translate_y", "name": "translate_y", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["probability", "magnitude"], "arg_types": ["builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "translate_y", "ret_type": "kornia.augmentation.auto.operations.base.OperationBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\kornia\\augmentation\\auto\\autoaugment\\ops.py"}