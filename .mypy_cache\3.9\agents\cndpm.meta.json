{"data_mtime": 1751962524, "dep_lines": [3, 5, 1, 2, 4, 5, 6, 7, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 20, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["models.ndpm.ndpm", "torch.utils.data", "agents.base", "continuum.data_utils", "utils.setup_elements", "torch.utils", "utils.utils", "torch", "numpy", "builtins", "_frozen_importlib", "_typeshed", "abc", "continuum", "models", "models.ndpm", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "torch._C", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.return_types", "torch.utils._contextlib", "torch.utils.data.dataloader", "torch.utils.data.dataset", "torch.utils.data.sampler", "typing", "utils"], "hash": "2d9958564e57122b4a338af30d95815b305dbc89", "id": "agents.cndpm", "ignore_all": true, "interface_hash": "c940de434de352b9ec95d343e7910db14522979c", "mtime": 1751894900, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\agents\\cndpm.py", "plugin_data": null, "size": 2674, "suppressed": [], "version_id": "1.15.0"}