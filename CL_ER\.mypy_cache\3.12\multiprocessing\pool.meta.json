{"data_mtime": 1751960785, "dep_lines": [2, 1, 3, 4, 5, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["collections.abc", "sys", "types", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "7e13c9847a3929d10e280c2d94b6502b3b05d264", "id": "multiprocessing.pool", "ignore_all": true, "interface_hash": "a21de20e3614bad84db99537b9a1df0cf73df94a", "mtime": 1751959050, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.mypy-type-checker-2024.2.0-universal\\bundled\\libs\\mypy\\typeshed\\stdlib\\multiprocessing\\pool.pyi", "plugin_data": null, "size": 3885, "suppressed": [], "version_id": "1.14.1"}