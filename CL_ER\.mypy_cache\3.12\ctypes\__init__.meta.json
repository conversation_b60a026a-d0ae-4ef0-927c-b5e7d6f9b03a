{"data_mtime": 1754286231, "dep_lines": [28, 1, 2, 27, 29, 30, 39, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["ctypes._endian", "sys", "_ctypes", "_typeshed", "typing", "typing_extensions", "types", "builtins", "_frozen_importlib", "abc", "os"], "hash": "8133075252f4bc1fe06331284ae1a6276446da80", "id": "ctypes", "ignore_all": true, "interface_hash": "e1ad73bf602c5a0a51087417ff2cabeb47f15350", "mtime": 1751962336, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\ctypes\\__init__.pyi", "plugin_data": null, "size": 9380, "suppressed": [], "version_id": "1.15.0"}