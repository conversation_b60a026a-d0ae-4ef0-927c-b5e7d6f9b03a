{".class": "MypyFile", "_fullname": "agents.APUMM_ER", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "APUMM_Buffer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["utils.buffer.buffer.Buffer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "agents.APUMM_ER.APUMM_Buffer", "name": "APUMM_Buffer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "agents.APUMM_ER.APUMM_Buffer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "agents.APUMM_ER", "mro": ["agents.APUMM_ER.APUMM_Buffer", "utils.buffer.buffer.Buffer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "model", "classifier", "args", "apumm_loss"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.APUMM_ER.APUMM_Buffer.__init__", "name": "__init__", "type": null}}, "apumm_loss": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.APUMM_ER.APUMM_Buffer.apumm_loss", "name": "apumm_loss", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "x", "y", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.APUMM_ER.APUMM_Buffer.update", "name": "update", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "agents.APUMM_ER.APUMM_Buffer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "agents.APUMM_ER.APUMM_Buffer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "APUMM_ER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["agents.prototype_anchored_er.PrototypeAnchoredER"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "agents.APUMM_ER.APUMM_ER", "name": "APUMM_ER", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "agents.APUMM_ER.APUMM_ER", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "agents.APUMM_ER", "mro": ["agents.APUMM_ER.APUMM_ER", "agents.prototype_anchored_er.PrototypeAnchoredER", "agents.exp_replay.ExperienceReplay", "agents.base.ContinualLearner", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "models", "opt", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.APUMM_ER.APUMM_ER.__init__", "name": "__init__", "type": null}}, "before_train": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x_train", "y_train"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.APUMM_ER.APUMM_ER.before_train", "name": "before_train", "type": null}}, "train_learner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x_train", "y_train"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.APUMM_ER.APUMM_ER.train_learner", "name": "train_learner", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "agents.APUMM_ER.APUMM_ER.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "agents.APUMM_ER.APUMM_ER", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "APUMM_loss": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "agents.APUMM_ER.APUMM_loss", "name": "APUMM_loss", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "agents.APUMM_ER.APUMM_loss", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "agents.APUMM_ER", "mro": ["agents.APUMM_ER.APUMM_loss", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.APUMM_ER.APUMM_loss.__init__", "name": "__init__", "type": null}}, "class_prototypes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.APUMM_ER.APUMM_loss.class_prototypes", "name": "class_prototypes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "confidence_threshold": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.APUMM_ER.APUMM_loss.confidence_threshold", "name": "confidence_threshold", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "current_task": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.APUMM_ER.APUMM_loss.current_task", "name": "current_task", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.APUMM_ER.APUMM_loss.ema", "name": "ema", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "estimate_uncertainty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "logits"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.APUMM_ER.APUMM_loss.estimate_uncertainty", "name": "estimate_uncertainty", "type": null}}, "feature_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.APUMM_ER.APUMM_loss.feature_dim", "name": "feature_dim", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_apumm_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "combined_features", "mem_y", "batch_y", "criterion", "logits", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.APUMM_ER.APUMM_loss.get_apumm_loss", "name": "get_apumm_loss", "type": null}}, "get_sample_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.APUMM_ER.APUMM_loss.get_sample_weights", "name": "get_sample_weights", "type": null}}, "momentum_schedule": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.APUMM_ER.APUMM_loss.momentum_schedule", "name": "momentum_schedule", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_classes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.APUMM_ER.APUMM_loss.num_classes", "name": "num_classes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "sample_weights": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.APUMM_ER.APUMM_loss.sample_weights", "name": "sample_weights", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "update_prototypes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "features", "labels", "confidence", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.APUMM_ER.APUMM_loss.update_prototypes", "name": "update_prototypes", "type": null}}, "update_task_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.APUMM_ER.APUMM_loss.update_task_id", "name": "update_task_id", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "agents.APUMM_ER.APUMM_loss.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "agents.APUMM_ER.APUMM_loss", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AverageMeter": {".class": "SymbolTableNode", "cross_ref": "utils.utils.AverageMeter", "kind": "Gdef"}, "Buffer": {".class": "SymbolTableNode", "cross_ref": "utils.buffer.buffer.Buffer", "kind": "Gdef"}, "ContinualLearner": {".class": "SymbolTableNode", "cross_ref": "agents.base.ContinualLearner", "kind": "Gdef"}, "F": {".class": "SymbolTableNode", "cross_ref": "torch.nn.functional", "kind": "Gdef"}, "PrototypeAnchoredER": {".class": "SymbolTableNode", "cross_ref": "agents.prototype_anchored_er.PrototypeAnchoredER", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.APUMM_ER.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.APUMM_ER.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.APUMM_ER.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.APUMM_ER.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.APUMM_ER.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.APUMM_ER.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "data": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data", "kind": "Gdef"}, "dataset_transform": {".class": "SymbolTableNode", "cross_ref": "continuum.data_utils.dataset_transform", "kind": "Gdef"}, "maybe_cuda": {".class": "SymbolTableNode", "cross_ref": "utils.utils.maybe_cuda", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "transforms_match": {".class": "SymbolTableNode", "cross_ref": "utils.setup_elements.transforms_match", "kind": "Gdef"}}, "path": "D:\\continual_learning\\CL_ER\\agents\\APUMM_ER.py"}