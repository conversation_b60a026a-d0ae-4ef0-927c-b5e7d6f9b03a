{".class": "MypyFile", "_fullname": "concurrent.futures", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ALL_COMPLETED": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.ALL_COMPLETED", "kind": "Gdef"}, "BrokenExecutor": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.BrokenExecutor", "kind": "Gdef"}, "CancelledError": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.CancelledError", "kind": "Gdef"}, "Executor": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.Executor", "kind": "Gdef"}, "FIRST_COMPLETED": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.FIRST_COMPLETED", "kind": "Gdef"}, "FIRST_EXCEPTION": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.FIRST_EXCEPTION", "kind": "Gdef"}, "Future": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.Future", "kind": "Gdef"}, "InvalidStateError": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.InvalidStateError", "kind": "Gdef", "module_public": false}, "ProcessPoolExecutor": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures.process.ProcessPoolExecutor", "kind": "Gdef"}, "ThreadPoolExecutor": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures.thread.ThreadPoolExecutor", "kind": "Gdef"}, "TimeoutError": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.TimeoutError", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "concurrent.futures.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__dir__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "concurrent.futures.__dir__", "name": "__dir__", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__dir__", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "concurrent.futures.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "as_completed": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.as_completed", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "wait": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.wait", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\concurrent\\futures\\__init__.pyi"}