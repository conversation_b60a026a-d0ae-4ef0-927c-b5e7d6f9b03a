{".class": "MypyFile", "_fullname": "agents.PAFL_ER", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AverageMeter": {".class": "SymbolTableNode", "cross_ref": "utils.utils.AverageMeter", "kind": "Gdef"}, "Buffer": {".class": "SymbolTableNode", "cross_ref": "utils.buffer.buffer.Buffer", "kind": "Gdef"}, "ContinualLearner": {".class": "SymbolTableNode", "cross_ref": "agents.base.ContinualLearner", "kind": "Gdef"}, "F": {".class": "SymbolTableNode", "cross_ref": "torch.nn.functional", "kind": "Gdef"}, "PAFL_Buffer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["utils.buffer.buffer.Buffer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "agents.PAFL_ER.PAFL_Buffer", "name": "PAFL_Buffer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_Buffer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "agents.PAFL_ER", "mro": ["agents.PAFL_ER.PAFL_Buffer", "utils.buffer.buffer.Buffer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "model", "classifier", "args", "pafl_loss"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_Buffer.__init__", "name": "__init__", "type": null}}, "buffer_task_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_Buffer.buffer_task_id", "name": "buffer_task_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "current_task_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_Buffer.current_task_id", "name": "current_task_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_all_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_Buffer.get_all_data", "name": "get_all_data", "type": null}}, "max_current_task_ratio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_Buffer.max_current_task_ratio", "name": "max_current_task_ratio", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "min_task_ratio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_Buffer.min_task_ratio", "name": "min_task_ratio", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pafl_loss": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_Buffer.pafl_loss", "name": "pafl_loss", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "print_buffer_task_distribution": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_Buffer.print_buffer_task_distribution", "name": "print_buffer_task_distribution", "type": null}}, "retrieve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_Buffer.retrieve", "name": "retrieve", "type": null}}, "set_current_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_Buffer.set_current_task", "name": "set_current_task", "type": null}}, "task_sample_counts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_Buffer.task_sample_counts", "name": "task_sample_counts", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_Buffer.update", "name": "update", "type": null}}, "update_after_forgetting": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "kept_x", "kept_y", "kept_task_ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_Buffer.update_after_forgetting", "name": "update_after_forgetting", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "agents.PAFL_ER.PAFL_Buffer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "agents.PAFL_ER.PAFL_Buffer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PAFL_ER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["agents.prototype_anchored_er.PrototypeAnchoredER"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "agents.PAFL_ER.PAFL_ER", "name": "PAFL_ER", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_ER", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "agents.PAFL_ER", "mro": ["agents.PAFL_ER.PAFL_ER", "agents.prototype_anchored_er.PrototypeAnchoredER", "agents.exp_replay.ExperienceReplay", "agents.base.ContinualLearner", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "models", "opt", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_ER.__init__", "name": "__init__", "type": null}}, "analyze_buffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_ER.analyze_buffer", "name": "analyze_buffer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "analyze_buffer_distribution": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_ER.analyze_buffer_distribution", "name": "analyze_buffer_distribution", "type": null}}, "before_train": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x_train", "y_train"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_ER.before_train", "name": "before_train", "type": null}}, "detailed_buffer_analysis": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_ER.detailed_buffer_analysis", "name": "detailed_buffer_analysis", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forget_strategy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_ER.forget_strategy", "name": "forget_strategy", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forget_vote_threshold": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_ER.forget_vote_threshold", "name": "forget_vote_threshold", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forget_weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_ER.forget_weight", "name": "forget_weight", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "max_forget_ratio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_ER.max_forget_ratio", "name": "max_forget_ratio", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "min_task_ratio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_ER.min_task_ratio", "name": "min_task_ratio", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "train_learner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x_train", "y_train"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_ER.train_learner", "name": "train_learner", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "agents.PAFL_ER.PAFL_ER.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "agents.PAFL_ER.PAFL_ER", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PAFL_loss": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "agents.PAFL_ER.PAFL_loss", "name": "PAFL_loss", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_loss", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "agents.PAFL_ER", "mro": ["agents.PAFL_ER.PAFL_loss", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_loss.__init__", "name": "__init__", "type": null}}, "apply_forgetting_strategies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "model", "mem_x", "mem_y", "batch_x", "batch_y", "epoch", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_loss.apply_forgetting_strategies", "name": "apply_forgetting_strategies", "type": null}}, "buffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_loss.buffer", "name": "buffer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "class_prototypes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_loss.class_prototypes", "name": "class_prototypes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "confidence_based_forgetting": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "model", "mem_x", "mem_y", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_loss.confidence_based_forgetting", "name": "confidence_based_forgetting", "type": null}}, "confidence_threshold": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_loss.confidence_threshold", "name": "confidence_threshold", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "cuda": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_loss.cuda", "name": "cuda", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "current_task": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_loss.current_task", "name": "current_task", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "distance_threshold": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_loss.distance_threshold", "name": "distance_threshold", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_loss.ema", "name": "ema", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "feature_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_loss.feature_dim", "name": "feature_dim", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forget_interval": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_loss.forget_interval", "name": "forget_interval", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forgotten_samples": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_loss.forgotten_samples", "name": "forgotten_samples", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_forgetting_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "model", "forgotten_x", "forgotten_y", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_loss.get_forgetting_loss", "name": "get_forgetting_loss", "type": null}}, "get_pa_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "combined_features", "mem_y", "batch_y", "criterion", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_loss.get_pa_loss", "name": "get_pa_loss", "type": null}}, "num_classes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_loss.num_classes", "name": "num_classes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "prototype_distance_forgetting": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "features", "labels", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_loss.prototype_distance_forgetting", "name": "prototype_distance_forgetting", "type": null}}, "relevance_threshold": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.PAFL_ER.PAFL_loss.relevance_threshold", "name": "relevance_threshold", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "task_relevance_forgetting": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "mem_features", "batch_features", "mem_y", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_loss.task_relevance_forgetting", "name": "task_relevance_forgetting", "type": null}}, "update_prototypes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "features", "labels", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_loss.update_prototypes", "name": "update_prototypes", "type": null}}, "update_task_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.PAFL_ER.PAFL_loss.update_task_id", "name": "update_task_id", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "agents.PAFL_ER.PAFL_loss.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "agents.PAFL_ER.PAFL_loss", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PrototypeAnchoredER": {".class": "SymbolTableNode", "cross_ref": "agents.prototype_anchored_er.PrototypeAnchoredER", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.PAFL_ER.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.PAFL_ER.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.PAFL_ER.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.PAFL_ER.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.PAFL_ER.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.PAFL_ER.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "data": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data", "kind": "Gdef"}, "dataset_transform": {".class": "SymbolTableNode", "cross_ref": "continuum.data_utils.dataset_transform", "kind": "Gdef"}, "maybe_cuda": {".class": "SymbolTableNode", "cross_ref": "utils.utils.maybe_cuda", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "transforms_match": {".class": "SymbolTableNode", "cross_ref": "utils.setup_elements.transforms_match", "kind": "Gdef"}}, "path": "D:\\continual_learning\\CL_ER\\agents\\PAFL_ER.py"}