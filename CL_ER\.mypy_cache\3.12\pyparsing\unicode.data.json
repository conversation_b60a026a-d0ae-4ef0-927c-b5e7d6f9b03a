{".class": "MypyFile", "_fullname": "pyparsing.unicode", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "UnicodeRangeList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pyparsing.unicode.UnicodeRangeList", "line": 28, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.unicode.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.unicode.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.unicode.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.unicode.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.unicode.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pyparsing.unicode.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_lazyclassproperty": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode._lazyclassproperty", "name": "_lazyclassproperty", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode._lazyclassproperty", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode._lazyclassproperty", "builtins.object"], "names": {".class": "SymbolTable", "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.unicode._lazyclassproperty.__get__", "name": "__get__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pyparsing.unicode._lazyclassproperty.__init__", "name": "__init__", "type": null}}, "__name__": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.unicode._lazyclassproperty.__name__", "name": "__name__", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pyparsing.unicode._lazyclassproperty.fn", "name": "fn", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode._lazyclassproperty.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode._lazyclassproperty", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "filterfalse": {".class": "SymbolTableNode", "cross_ref": "itertools.filterfalse", "kind": "Gdef"}, "pyparsing_unicode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.unicode.unicode_set"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode.pyparsing_unicode", "name": "pyparsing_unicode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode.pyparsing_unicode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode.pyparsing_unicode", "pyparsing.unicode.unicode_set", "builtins.object"], "names": {".class": "SymbolTable", "Arabic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.unicode.unicode_set"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode.pyparsing_unicode.Arabic", "name": "Arabic", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode.pyparsing_unicode.Arabic", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode.pyparsing_unicode.Arabic", "pyparsing.unicode.unicode_set", "builtins.object"], "names": {".class": "SymbolTable", "_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.unicode.pyparsing_unicode.Arabic._ranges", "name": "_ranges", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.unicode.UnicodeRangeList"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode.pyparsing_unicode.Arabic.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode.pyparsing_unicode.Arabic", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BasicMultilingualPlane": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.unicode.unicode_set"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode.pyparsing_unicode.BasicMultilingualPlane", "name": "BasicMultilingualPlane", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode.pyparsing_unicode.BasicMultilingualPlane", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode.pyparsing_unicode.BasicMultilingualPlane", "pyparsing.unicode.unicode_set", "builtins.object"], "names": {".class": "SymbolTable", "_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.unicode.pyparsing_unicode.BasicMultilingualPlane._ranges", "name": "_ranges", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.unicode.UnicodeRangeList"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode.pyparsing_unicode.BasicMultilingualPlane.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode.pyparsing_unicode.BasicMultilingualPlane", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CJK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.unicode.pyparsing_unicode.Chinese", "pyparsing.unicode.pyparsing_unicode.Japanese", "pyparsing.unicode.pyparsing_unicode.Hangul"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode.pyparsing_unicode.CJK", "name": "CJK", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode.pyparsing_unicode.CJK", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode.pyparsing_unicode.CJK", "pyparsing.unicode.pyparsing_unicode.Chinese", "pyparsing.unicode.pyparsing_unicode.Japanese", "pyparsing.unicode.pyparsing_unicode.Hangul", "pyparsing.unicode.unicode_set", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode.pyparsing_unicode.CJK.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode.pyparsing_unicode.CJK", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Chinese": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.unicode.unicode_set"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode.pyparsing_unicode.Chinese", "name": "Chinese", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode.pyparsing_unicode.Chinese", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode.pyparsing_unicode.Chinese", "pyparsing.unicode.unicode_set", "builtins.object"], "names": {".class": "SymbolTable", "_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.unicode.pyparsing_unicode.Chinese._ranges", "name": "_ranges", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.unicode.UnicodeRangeList"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode.pyparsing_unicode.Chinese.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode.pyparsing_unicode.Chinese", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Cyrillic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.unicode.unicode_set"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode.pyparsing_unicode.Cyrillic", "name": "Cyrillic", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode.pyparsing_unicode.Cyrillic", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode.pyparsing_unicode.Cyrillic", "pyparsing.unicode.unicode_set", "builtins.object"], "names": {".class": "SymbolTable", "_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.unicode.pyparsing_unicode.Cyrillic._ranges", "name": "_ranges", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.unicode.UnicodeRangeList"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode.pyparsing_unicode.Cyrillic.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode.pyparsing_unicode.Cyrillic", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Devanagari": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.unicode.unicode_set"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode.pyparsing_unicode.Devanagari", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode.pyparsing_unicode.Devanagari", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode.pyparsing_unicode.Devanagari", "pyparsing.unicode.unicode_set", "builtins.object"], "names": {".class": "SymbolTable", "_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.unicode.pyparsing_unicode.Devanagari._ranges", "name": "_ranges", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.unicode.UnicodeRangeList"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode.pyparsing_unicode.Devanagari.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode.pyparsing_unicode.Devanagari", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Greek": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.unicode.unicode_set"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode.pyparsing_unicode.Greek", "name": "Greek", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode.pyparsing_unicode.Greek", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode.pyparsing_unicode.Greek", "pyparsing.unicode.unicode_set", "builtins.object"], "names": {".class": "SymbolTable", "_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.unicode.pyparsing_unicode.Greek._ranges", "name": "_ranges", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.unicode.UnicodeRangeList"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode.pyparsing_unicode.Greek.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode.pyparsing_unicode.Greek", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Hangul": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.unicode.unicode_set"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode.pyparsing_unicode.Hangul", "name": "Hangul", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode.pyparsing_unicode.Hangul", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode.pyparsing_unicode.Hangul", "pyparsing.unicode.unicode_set", "builtins.object"], "names": {".class": "SymbolTable", "_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.unicode.pyparsing_unicode.Hangul._ranges", "name": "_ranges", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.unicode.UnicodeRangeList"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode.pyparsing_unicode.Hangul.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode.pyparsing_unicode.Hangul", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Hebrew": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.unicode.unicode_set"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode.pyparsing_unicode.Hebrew", "name": "Hebrew", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode.pyparsing_unicode.Hebrew", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode.pyparsing_unicode.Hebrew", "pyparsing.unicode.unicode_set", "builtins.object"], "names": {".class": "SymbolTable", "_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.unicode.pyparsing_unicode.Hebrew._ranges", "name": "_ranges", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.unicode.UnicodeRangeList"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode.pyparsing_unicode.Hebrew.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode.pyparsing_unicode.Hebrew", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Japanese": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.unicode.unicode_set"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode.pyparsing_unicode.Japanese", "name": "Japanese", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode.pyparsing_unicode.Japanese", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode.pyparsing_unicode.Japanese", "pyparsing.unicode.unicode_set", "builtins.object"], "names": {".class": "SymbolTable", "Hiragana": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.unicode.unicode_set"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode.pyparsing_unicode.Japanese.Hiragana", "name": "Hi<PERSON>na", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode.pyparsing_unicode.Japanese.Hiragana", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode.pyparsing_unicode.Japanese.Hiragana", "pyparsing.unicode.unicode_set", "builtins.object"], "names": {".class": "SymbolTable", "_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.unicode.pyparsing_unicode.Japanese.Hiragana._ranges", "name": "_ranges", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.unicode.UnicodeRangeList"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode.pyparsing_unicode.Japanese.Hiragana.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode.pyparsing_unicode.Japanese.Hiragana", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Kanji": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.unicode.unicode_set"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode.pyparsing_unicode.Japanese.Kanji", "name": "Kanji", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode.pyparsing_unicode.Japanese.Kanji", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode.pyparsing_unicode.Japanese.Kanji", "pyparsing.unicode.unicode_set", "builtins.object"], "names": {".class": "SymbolTable", "_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.unicode.pyparsing_unicode.Japanese.Kanji._ranges", "name": "_ranges", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.unicode.UnicodeRangeList"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode.pyparsing_unicode.Japanese.Kanji.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode.pyparsing_unicode.Japanese.Kanji", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Katakana": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.unicode.unicode_set"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode.pyparsing_unicode.Japanese.Katakana", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode.pyparsing_unicode.Japanese.Katakana", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode.pyparsing_unicode.Japanese.Katakana", "pyparsing.unicode.unicode_set", "builtins.object"], "names": {".class": "SymbolTable", "_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.unicode.pyparsing_unicode.Japanese.Katakana._ranges", "name": "_ranges", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.unicode.UnicodeRangeList"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode.pyparsing_unicode.Japanese.Katakana.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode.pyparsing_unicode.Japanese.Katakana", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.unicode.pyparsing_unicode.Japanese._ranges", "name": "_ranges", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.unicode.UnicodeRangeList"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode.pyparsing_unicode.Japanese.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode.pyparsing_unicode.Japanese", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Korean": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pyparsing.unicode.pyparsing_unicode.Korean", "name": "Korean", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["pyparsing.unicode.pyparsing_unicode.Hangul"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pyparsing.unicode.pyparsing_unicode.Hangul", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Latin1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.unicode.unicode_set"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode.pyparsing_unicode.Latin1", "name": "Latin1", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode.pyparsing_unicode.Latin1", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode.pyparsing_unicode.Latin1", "pyparsing.unicode.unicode_set", "builtins.object"], "names": {".class": "SymbolTable", "_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.unicode.pyparsing_unicode.Latin1._ranges", "name": "_ranges", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.unicode.UnicodeRangeList"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode.pyparsing_unicode.Latin1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode.pyparsing_unicode.Latin1", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LatinA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.unicode.unicode_set"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode.pyparsing_unicode.LatinA", "name": "LatinA", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode.pyparsing_unicode.LatinA", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode.pyparsing_unicode.LatinA", "pyparsing.unicode.unicode_set", "builtins.object"], "names": {".class": "SymbolTable", "_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.unicode.pyparsing_unicode.LatinA._ranges", "name": "_ranges", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.unicode.UnicodeRangeList"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode.pyparsing_unicode.LatinA.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode.pyparsing_unicode.LatinA", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LatinB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.unicode.unicode_set"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode.pyparsing_unicode.LatinB", "name": "LatinB", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode.pyparsing_unicode.LatinB", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode.pyparsing_unicode.LatinB", "pyparsing.unicode.unicode_set", "builtins.object"], "names": {".class": "SymbolTable", "_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.unicode.pyparsing_unicode.LatinB._ranges", "name": "_ranges", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.unicode.UnicodeRangeList"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode.pyparsing_unicode.LatinB.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode.pyparsing_unicode.LatinB", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Thai": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pyparsing.unicode.unicode_set"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode.pyparsing_unicode.Thai", "name": "Thai", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode.pyparsing_unicode.Thai", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode.pyparsing_unicode.Thai", "pyparsing.unicode.unicode_set", "builtins.object"], "names": {".class": "SymbolTable", "_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.unicode.pyparsing_unicode.Thai._ranges", "name": "_ranges", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.unicode.UnicodeRangeList"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode.pyparsing_unicode.Thai.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode.pyparsing_unicode.Thai", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.unicode.pyparsing_unicode._ranges", "name": "_ranges", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.unicode.UnicodeRangeList"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode.pyparsing_unicode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode.pyparsing_unicode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "unicode_set": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pyparsing.unicode.unicode_set", "name": "unicode_set", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pyparsing.unicode.unicode_set", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pyparsing.unicode", "mro": ["pyparsing.unicode.unicode_set", "builtins.object"], "names": {".class": "SymbolTable", "_chars_for_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pyparsing.unicode.unicode_set._chars_for_ranges", "name": "_chars_for_ranges", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pyparsing.unicode.unicode_set._chars_for_ranges", "name": "_chars_for_ranges", "type": "pyparsing.unicode._lazyclassproperty"}}}, "_ranges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pyparsing.unicode.unicode_set._ranges", "name": "_ranges", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pyparsing.unicode.UnicodeRangeList"}}}, "alphanums": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pyparsing.unicode.unicode_set.alphanums", "name": "alphanums", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pyparsing.unicode.unicode_set.alphanums", "name": "alphanums", "type": "pyparsing.unicode._lazyclassproperty"}}}, "alphas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pyparsing.unicode.unicode_set.alphas", "name": "alphas", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pyparsing.unicode.unicode_set.alphas", "name": "alphas", "type": "pyparsing.unicode._lazyclassproperty"}}}, "identbodychars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pyparsing.unicode.unicode_set.identbodychars", "name": "identbodychars", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pyparsing.unicode.unicode_set.identbodychars", "name": "identbodychars", "type": "pyparsing.unicode._lazyclassproperty"}}}, "identchars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pyparsing.unicode.unicode_set.identchars", "name": "identchars", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pyparsing.unicode.unicode_set.identchars", "name": "identchars", "type": "pyparsing.unicode._lazyclassproperty"}}}, "nums": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pyparsing.unicode.unicode_set.nums", "name": "nums", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pyparsing.unicode.unicode_set.nums", "name": "nums", "type": "pyparsing.unicode._lazyclassproperty"}}}, "printables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pyparsing.unicode.unicode_set.printables", "name": "printables", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pyparsing.unicode.unicode_set.printables", "name": "printables", "type": "pyparsing.unicode._lazyclassproperty"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pyparsing.unicode.unicode_set.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pyparsing.unicode.unicode_set", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "path": "d:\\anaconda3\\Lib\\site-packages\\pyparsing\\unicode.py"}