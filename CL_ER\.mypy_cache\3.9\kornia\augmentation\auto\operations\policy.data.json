{".class": "MypyFile", "_fullname": "kornia.augmentation.auto.operations.policy", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ImageSequentialBase": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.container.base.ImageSequentialBase", "kind": "Gdef"}, "InputSequentialOps": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.container.ops.InputSequentialOps", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "K": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Module": {".class": "SymbolTableNode", "cross_ref": "kornia.core._backend.Module", "kind": "Gdef"}, "OperationBase": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.auto.operations.base.OperationBase", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ParamItem": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.container.params.ParamItem", "kind": "Gdef"}, "PolicySequential": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["kornia.augmentation.container.base.TransformMatrixMinIn", "kornia.augmentation.container.base.ImageSequentialBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "kornia.augmentation.auto.operations.policy.PolicySequential", "name": "PolicySequential", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.policy.PolicySequential", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "kornia.augmentation.auto.operations.policy", "mro": ["kornia.augmentation.auto.operations.policy.PolicySequential", "kornia.augmentation.container.base.TransformMatrixMinIn", "kornia.augmentation.container.base.ImageSequentialBase", "kornia.augmentation.container.base.SequentialBase", "kornia.augmentation.container.base.BasicSequentialBase", "torch.nn.modules.container.Sequential", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "operations"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.policy.PolicySequential.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "operations"], "arg_types": ["kornia.augmentation.auto.operations.policy.PolicySequential", "kornia.augmentation.auto.operations.base.OperationBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PolicySequential", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_update_transform_matrix_for_valid_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.policy.PolicySequential._update_transform_matrix_for_valid_op", "name": "_update_transform_matrix_for_valid_op", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "module"], "arg_types": ["kornia.augmentation.auto.operations.policy.PolicySequential", "torch.nn.modules.module.Module"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_transform_matrix_for_valid_op of PolicySequential", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_valid_ops_for_transform_computation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "kornia.augmentation.auto.operations.policy.PolicySequential._valid_ops_for_transform_computation", "name": "_valid_ops_for_transform_computation", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "clear_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.policy.PolicySequential.clear_state", "name": "clear_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["kornia.augmentation.auto.operations.policy.PolicySequential"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_state of PolicySequential", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "batch_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.policy.PolicySequential.forward_parameters", "name": "forward_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "batch_shape"], "arg_types": ["kornia.augmentation.auto.operations.policy.PolicySequential", "torch._<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward_parameters of PolicySequential", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "kornia.augmentation.container.params.ParamItem"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_forward_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.policy.PolicySequential.get_forward_sequence", "name": "get_forward_sequence", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "params"], "arg_types": ["kornia.augmentation.auto.operations.policy.PolicySequential", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "kornia.augmentation.container.params.ParamItem"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_forward_sequence of PolicySequential", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "torch.nn.modules.module.Module"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_transformation_matrix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "input", "params", "recompute", "extra_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.policy.PolicySequential.get_transformation_matrix", "name": "get_transformation_matrix", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "input", "params", "recompute", "extra_args"], "arg_types": ["kornia.augmentation.auto.operations.policy.PolicySequential", "torch._tensor.Tensor", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "kornia.augmentation.container.params.ParamItem"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_transformation_matrix of PolicySequential", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "identity_matrix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.policy.PolicySequential.identity_matrix", "name": "identity_matrix", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "input"], "arg_types": ["kornia.augmentation.auto.operations.policy.PolicySequential", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity_matrix of PolicySequential", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_intensity_only": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.policy.PolicySequential.is_intensity_only", "name": "is_intensity_only", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["kornia.augmentation.auto.operations.policy.PolicySequential"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_intensity_only of PolicySequential", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transform_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "input", "params", "extra_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.policy.PolicySequential.transform_inputs", "name": "transform_inputs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "input", "params", "extra_args"], "arg_types": ["kornia.augmentation.auto.operations.policy.PolicySequential", "torch._tensor.Tensor", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "kornia.augmentation.container.params.ParamItem"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transform_inputs of PolicySequential", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "validate_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "operations"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "kornia.augmentation.auto.operations.policy.PolicySequential.validate_operations", "name": "validate_operations", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "operations"], "arg_types": ["kornia.augmentation.auto.operations.policy.PolicySequential", "kornia.augmentation.auto.operations.base.OperationBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_operations of PolicySequential", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "kornia.augmentation.auto.operations.policy.PolicySequential.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "kornia.augmentation.auto.operations.policy.PolicySequential", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Size": {".class": "SymbolTableNode", "cross_ref": "torch._<PERSON><PERSON>", "kind": "Gdef"}, "Tensor": {".class": "SymbolTableNode", "cross_ref": "kornia.core._backend.Tensor", "kind": "Gdef"}, "TransformMatrixMinIn": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.container.base.TransformMatrixMinIn", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.policy.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.policy.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.policy.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.policy.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.policy.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "kornia.augmentation.auto.operations.policy.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_transform_input": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.utils.helpers._transform_input", "kind": "Gdef"}, "as_tensor": {".class": "SymbolTableNode", "cross_ref": "kornia.core._backend.as_tensor", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "eye_like": {".class": "SymbolTableNode", "cross_ref": "kornia.utils.misc.eye_like", "kind": "Gdef"}, "override_parameters": {".class": "SymbolTableNode", "cross_ref": "kornia.augmentation.utils.helpers.override_parameters", "kind": "Gdef"}}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\kornia\\augmentation\\auto\\operations\\policy.py"}