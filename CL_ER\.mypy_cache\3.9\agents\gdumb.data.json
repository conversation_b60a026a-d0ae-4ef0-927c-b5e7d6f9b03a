{".class": "MypyFile", "_fullname": "agents.gdumb", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ContinualLearner": {".class": "SymbolTableNode", "cross_ref": "agents.base.ContinualLearner", "kind": "Gdef"}, "EarlyStopping": {".class": "SymbolTableNode", "cross_ref": "utils.utils.EarlyStopping", "kind": "Gdef"}, "Gdumb": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["agents.base.ContinualLearner"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "agents.gdumb.Gdumb", "name": "Gdumb", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "agents.gdumb.Gdumb", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "agents.gdumb", "mro": ["agents.gdumb.Gdumb", "agents.base.ContinualLearner", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "opt", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.gdumb.Gdumb.__init__", "name": "__init__", "type": null}}, "greedy_balancing_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.gdumb.Gdumb.greedy_balancing_update", "name": "greedy_balancing_update", "type": null}}, "mem_c": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.gdumb.Gdumb.mem_c", "name": "mem_c", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "mem_img": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.gdumb.Gdumb.mem_img", "name": "mem_img", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "train_learner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x_train", "y_train"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.gdumb.Gdumb.train_learner", "name": "train_learner", "type": null}}, "train_mem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.gdumb.Gdumb.train_mem", "name": "train_mem", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "agents.gdumb.Gdumb.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "agents.gdumb.Gdumb", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.gdumb.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.gdumb.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.gdumb.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.gdumb.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.gdumb.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.gdumb.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "data": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data", "kind": "Gdef"}, "dataset_transform": {".class": "SymbolTableNode", "cross_ref": "continuum.data_utils.dataset_transform", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "maybe_cuda": {".class": "SymbolTableNode", "cross_ref": "utils.utils.maybe_cuda", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "setup_architecture": {".class": "SymbolTableNode", "cross_ref": "utils.setup_elements.setup_architecture", "kind": "Gdef"}, "setup_opt": {".class": "SymbolTableNode", "cross_ref": "utils.setup_elements.setup_opt", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "transforms_match": {".class": "SymbolTableNode", "cross_ref": "utils.setup_elements.transforms_match", "kind": "Gdef"}}, "path": "D:\\continual_learning\\CL_ER\\agents\\gdumb.py"}