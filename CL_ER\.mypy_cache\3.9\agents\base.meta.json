{"data_mtime": 1754286428, "dep_lines": [5, 8, 5, 6, 7, 10, 1, 3, 4, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 5, 5, 5, 5, 10, 10, 10, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.nn.functional", "torch.utils.data", "torch.nn", "utils.kd_manager", "utils.utils", "utils.loss", "abc", "numpy", "torch", "copy", "pickle", "builtins", "string", "torch.distributed._functional_collectives", "types", "itertools", "contextlib", "traceback", "html", "operator", "os", "re", "sys", "typing", "multiprocessing.reduction", "collections", "warnings", "pprint", "inspect", "math", "_frozen_importlib", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.nn.modules", "torch.nn.modules.module", "torch.utils", "torch.utils.data.dataloader", "torch.utils.data.dataset", "torch.utils.data.sampler", "utils"], "hash": "befab6d60a9e8d25cfcd714364b7a06dd2a7c3db", "id": "agents.base", "ignore_all": true, "interface_hash": "c34b99d1965c9962afe86410e704fbf79b35290b", "mtime": 1751894900, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\agents\\base.py", "plugin_data": null, "size": 6375, "suppressed": [], "version_id": "1.15.0"}