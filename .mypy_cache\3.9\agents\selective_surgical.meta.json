{"data_mtime": 1754286791, "dep_lines": [3, 2, 5, 1, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1, 587], "dep_prios": [10, 10, 5, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["torch.nn.functional", "torch.nn", "agents.exp_replay", "torch", "numpy", "copy", "builtins", "_frozen_importlib", "_typeshed", "abc", "agents.base", "torch.nn.modules", "torch.nn.modules.module", "typing"], "hash": "277cc7500f673193d12f940677ac74fb016dc997", "id": "agents.selective_surgical", "ignore_all": true, "interface_hash": "440a20e9d4e33b0dfa5014d575c6bafc3097dda8", "mtime": 1751955555, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\agents\\selective_surgical.py", "plugin_data": null, "size": 24320, "suppressed": ["sklearn.metrics"], "version_id": "1.15.0"}