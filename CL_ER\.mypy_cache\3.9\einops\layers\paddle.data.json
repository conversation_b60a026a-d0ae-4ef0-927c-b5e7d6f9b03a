{".class": "MypyFile", "_fullname": "einops.layers.paddle", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EinMix": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["einops.layers._einmix._EinmixMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops.layers.paddle.EinMix", "name": "EinMix", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "einops.layers.paddle.EinMix", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops.layers.paddle", "mro": ["einops.layers.paddle.EinMix", "einops.layers._einmix._EinmixMixin", "builtins.object"], "names": {".class": "SymbolTable", "_create_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "weight_shape", "weight_bound", "bias_shape", "bias_bound"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops.layers.paddle.EinMix._create_parameters", "name": "_create_parameters", "type": null}}, "_create_rearrange_layers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "pre_reshape_pattern", "pre_reshape_lengths", "post_reshape_pattern", "post_reshape_lengths"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops.layers.paddle.EinMix._create_rearrange_layers", "name": "_create_rearrange_layers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "pre_reshape_pattern", "pre_reshape_lengths", "post_reshape_pattern", "post_reshape_lengths"], "arg_types": ["einops.layers.paddle.EinMix", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_rearrange_layers of EinMix", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops.layers.paddle.EinMix.bias", "name": "bias", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops.layers.paddle.EinMix.forward", "name": "forward", "type": null}}, "post_rearrange": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops.layers.paddle.EinMix.post_rearrange", "name": "post_rearrange", "type": {".class": "UnionType", "items": ["einops.layers.paddle.Rearrange", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "pre_rearrange": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops.layers.paddle.EinMix.pre_rearrange", "name": "pre_rearrange", "type": {".class": "UnionType", "items": ["einops.layers.paddle.Rearrange", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "einops.layers.paddle.EinMix.weight", "name": "weight", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops.layers.paddle.EinMix.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops.layers.paddle.EinMix", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Rearrange": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["einops.layers.RearrangeMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops.layers.paddle.Rearrange", "name": "Rearrange", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "einops.layers.paddle.Rearrange", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops.layers.paddle", "mro": ["einops.layers.paddle.Rearrange", "einops.layers.RearrangeMixin", "builtins.object"], "names": {".class": "SymbolTable", "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops.layers.paddle.Rearrange.forward", "name": "forward", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops.layers.paddle.Rearrange.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops.layers.paddle.Rearrange", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RearrangeMixin": {".class": "SymbolTableNode", "cross_ref": "einops.layers.RearrangeMixin", "kind": "Gdef"}, "Reduce": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["einops.layers.ReduceMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops.layers.paddle.Reduce", "name": "Reduce", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "einops.layers.paddle.Reduce", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops.layers.paddle", "mro": ["einops.layers.paddle.Reduce", "einops.layers.ReduceMixin", "builtins.object"], "names": {".class": "SymbolTable", "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "einops.layers.paddle.Reduce.forward", "name": "forward", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops.layers.paddle.Reduce.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops.layers.paddle.Reduce", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReduceMixin": {".class": "SymbolTableNode", "cross_ref": "einops.layers.ReduceMixin", "kind": "Gdef"}, "_EinmixMixin": {".class": "SymbolTableNode", "cross_ref": "einops.layers._einmix._EinmixMixin", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.layers.paddle.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__author__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "einops.layers.paddle.__author__", "name": "__author__", "type": "builtins.str"}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.layers.paddle.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.layers.paddle.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.layers.paddle.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.layers.paddle.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.layers.paddle.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "paddle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "einops.layers.paddle.paddle", "name": "paddle", "type": {".class": "AnyType", "missing_import_name": "einops.layers.paddle.paddle", "source_any": null, "type_of_any": 3}}}}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\einops\\layers\\paddle.py"}