{"data_mtime": 1754286230, "dep_lines": [1, 2, 6, 7, 8, 1, 3, 4, 5, 15, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 20, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["importlib.abc", "importlib.machinery", "collections.abc", "importlib._bootstrap", "importlib._bootstrap_external", "importlib", "sys", "types", "_typeshed", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "abc", "importlib._abc", "typing"], "hash": "e6d1abdef23db5be7b6cfbb0e1f7ee7d725f484c", "id": "importlib.util", "ignore_all": true, "interface_hash": "7adbf2fea05aeb0ce7253a93b10e58c4ed879853", "mtime": 1751962336, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\importlib\\util.pyi", "plugin_data": null, "size": 1397, "suppressed": [], "version_id": "1.15.0"}