{".class": "MypyFile", "_fullname": "agents.prototype_anchored_er", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AverageMeter": {".class": "SymbolTableNode", "cross_ref": "utils.utils.AverageMeter", "kind": "Gdef"}, "Buffer": {".class": "SymbolTableNode", "cross_ref": "utils.buffer.buffer.Buffer", "kind": "Gdef"}, "ContinualLearner": {".class": "SymbolTableNode", "cross_ref": "agents.base.ContinualLearner", "kind": "Gdef"}, "ExperienceReplay": {".class": "SymbolTableNode", "cross_ref": "agents.exp_replay.ExperienceReplay", "kind": "Gdef"}, "F": {".class": "SymbolTableNode", "cross_ref": "torch.nn.functional", "kind": "Gdef"}, "PA_loss": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "agents.prototype_anchored_er.PA_loss", "name": "PA_loss", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "agents.prototype_anchored_er.PA_loss", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "agents.prototype_anchored_er", "mro": ["agents.prototype_anchored_er.PA_loss", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.prototype_anchored_er.PA_loss.__init__", "name": "__init__", "type": null}}, "class_prototypes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.prototype_anchored_er.PA_loss.class_prototypes", "name": "class_prototypes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.prototype_anchored_er.PA_loss.ema", "name": "ema", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "feature_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.prototype_anchored_er.PA_loss.feature_dim", "name": "feature_dim", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_pa_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "combined_features", "mem_y", "batch_y", "criterion", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.prototype_anchored_er.PA_loss.get_pa_loss", "name": "get_pa_loss", "type": null}}, "num_classes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.prototype_anchored_er.PA_loss.num_classes", "name": "num_classes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "update_prototypes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "features", "labels", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.prototype_anchored_er.PA_loss.update_prototypes", "name": "update_prototypes", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "agents.prototype_anchored_er.PA_loss.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "agents.prototype_anchored_er.PA_loss", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PrototypeAnchoredER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["agents.exp_replay.ExperienceReplay"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "agents.prototype_anchored_er.PrototypeAnchoredER", "name": "PrototypeAnchoredER", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "agents.prototype_anchored_er.PrototypeAnchoredER", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "agents.prototype_anchored_er", "mro": ["agents.prototype_anchored_er.PrototypeAnchoredER", "agents.exp_replay.ExperienceReplay", "agents.base.ContinualLearner", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "models", "opt", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.prototype_anchored_er.PrototypeAnchoredER.__init__", "name": "__init__", "type": null}}, "analyze_buffer_distribution": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.prototype_anchored_er.PrototypeAnchoredER.analyze_buffer_distribution", "name": "analyze_buffer_distribution", "type": null}}, "forget_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "forget_task_id", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.prototype_anchored_er.PrototypeAnchoredER.forget_task", "name": "forget_task", "type": null}}, "get_task_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.prototype_anchored_er.PrototypeAnchoredER.get_task_data", "name": "get_task_data", "type": null}}, "load_model_and_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "model_path", "buffer_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.prototype_anchored_er.PrototypeAnchoredER.load_model_and_buffer", "name": "load_model_and_buffer", "type": null}}, "pa_loss": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.prototype_anchored_er.PrototypeAnchoredER.pa_loss", "name": "pa_loss", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "prototype_weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.prototype_anchored_er.PrototypeAnchoredER.prototype_weight", "name": "prototype_weight", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "save_dir": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.prototype_anchored_er.PrototypeAnchoredER.save_dir", "name": "save_dir", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "save_model_and_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.prototype_anchored_er.PrototypeAnchoredER.save_model_and_buffer", "name": "save_model_and_buffer", "type": null}}, "task_samples": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.prototype_anchored_er.PrototypeAnchoredER.task_samples", "name": "task_samples", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "task_total_samples": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.prototype_anchored_er.PrototypeAnchoredER.task_total_samples", "name": "task_total_samples", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "tasks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.prototype_anchored_er.PrototypeAnchoredER.tasks", "name": "tasks", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "train_learner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "x_train", "y_train", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.prototype_anchored_er.PrototypeAnchoredER.train_learner", "name": "train_learner", "type": null}}, "update_buffer_with_task_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "x", "y", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.prototype_anchored_er.PrototypeAnchoredER.update_buffer_with_task_id", "name": "update_buffer_with_task_id", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "agents.prototype_anchored_er.PrototypeAnchoredER.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "agents.prototype_anchored_er.PrototypeAnchoredER", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.prototype_anchored_er.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.prototype_anchored_er.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.prototype_anchored_er.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.prototype_anchored_er.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.prototype_anchored_er.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.prototype_anchored_er.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "data": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data", "kind": "Gdef"}, "dataset_transform": {".class": "SymbolTableNode", "cross_ref": "continuum.data_utils.dataset_transform", "kind": "Gdef"}, "maybe_cuda": {".class": "SymbolTableNode", "cross_ref": "utils.utils.maybe_cuda", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "save_model": {".class": "SymbolTableNode", "cross_ref": "utils.io.save_model", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "transforms_match": {".class": "SymbolTableNode", "cross_ref": "utils.setup_elements.transforms_match", "kind": "Gdef"}}, "path": "D:\\continual_learning\\CL_ER\\agents\\prototype_anchored_er.py"}