{".class": "MypyFile", "_fullname": "importlib.machinery", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BYTECODE_SUFFIXES": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.BYTECODE_SUFFIXES", "kind": "Gdef"}, "BuiltinImporter": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib.BuiltinImporter", "kind": "Gdef"}, "DEBUG_BYTECODE_SUFFIXES": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.DEBUG_BYTECODE_SUFFIXES", "kind": "Gdef"}, "EXTENSION_SUFFIXES": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.EXTENSION_SUFFIXES", "kind": "Gdef"}, "ExtensionFileLoader": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.ExtensionFileLoader", "kind": "Gdef"}, "FileFinder": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.FileFinder", "kind": "Gdef"}, "FrozenImporter": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib.FrozenImporter", "kind": "Gdef"}, "ModuleSpec": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib.ModuleSpec", "kind": "Gdef"}, "OPTIMIZED_BYTECODE_SUFFIXES": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.OPTIMIZED_BYTECODE_SUFFIXES", "kind": "Gdef"}, "PathFinder": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.PathFinder", "kind": "Gdef"}, "SOURCE_SUFFIXES": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.SOURCE_SUFFIXES", "kind": "Gdef"}, "SourceFileLoader": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.SourceFileLoader", "kind": "Gdef"}, "SourcelessFileLoader": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.SourcelessFileLoader", "kind": "Gdef"}, "WindowsRegistryFinder": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.WindowsRegistryFinder", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.machinery.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.machinery.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.machinery.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.machinery.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.machinery.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib.machinery.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "all_suffixes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "importlib.machinery.all_suffixes", "name": "all_suffixes", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "all_suffixes", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\importlib\\machinery.pyi"}