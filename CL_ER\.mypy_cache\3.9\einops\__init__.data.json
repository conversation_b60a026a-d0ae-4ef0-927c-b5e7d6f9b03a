{".class": "MypyFile", "_fullname": "einops", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "EinopsError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.RuntimeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "einops.EinopsError", "name": "EinopsError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "einops.EinopsError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "einops", "mro": ["einops.EinopsError", "builtins.RuntimeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "einops.EinopsError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "einops.EinopsError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "einops.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__author__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "einops.__author__", "name": "__author__", "type": "builtins.str"}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "einops.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "einops.__version__", "name": "__version__", "type": "builtins.str"}}, "asnumpy": {".class": "SymbolTableNode", "cross_ref": "einops.einops.asnumpy", "kind": "Gdef"}, "einsum": {".class": "SymbolTableNode", "cross_ref": "einops.einops.einsum", "kind": "Gdef"}, "pack": {".class": "SymbolTableNode", "cross_ref": "einops.packing.pack", "kind": "Gdef"}, "parse_shape": {".class": "SymbolTableNode", "cross_ref": "einops.einops.parse_shape", "kind": "Gdef"}, "rearrange": {".class": "SymbolTableNode", "cross_ref": "einops.einops.rearrange", "kind": "Gdef"}, "reduce": {".class": "SymbolTableNode", "cross_ref": "einops.einops.reduce", "kind": "Gdef"}, "repeat": {".class": "SymbolTableNode", "cross_ref": "einops.einops.repeat", "kind": "Gdef"}, "unpack": {".class": "SymbolTableNode", "cross_ref": "einops.packing.unpack", "kind": "Gdef"}}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\einops\\__init__.py"}