{".class": "MypyFile", "_fullname": "filelock", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AcquireReturnProxy": {".class": "SymbolTableNode", "cross_ref": "filelock._api.AcquireReturnProxy", "kind": "Gdef"}, "BaseFileLock": {".class": "SymbolTableNode", "cross_ref": "filelock._api.BaseFileLock", "kind": "Gdef"}, "FileLock": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "filelock.FileLock", "line": 36, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "filelock._soft.SoftFileLock"}}, "SoftFileLock": {".class": "SymbolTableNode", "cross_ref": "filelock._soft.SoftFileLock", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Timeout": {".class": "SymbolTableNode", "cross_ref": "filelock._error.Timeout", "kind": "Gdef"}, "UnixFileLock": {".class": "SymbolTableNode", "cross_ref": "filelock._unix.UnixFileLock", "kind": "Gdef"}, "WindowsFileLock": {".class": "SymbolTableNode", "cross_ref": "filelock._windows.WindowsFileLock", "kind": "Gdef"}, "_FileLock": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "filelock._FileLock", "name": "_FileLock", "type": {".class": "TypeType", "item": "filelock._api.BaseFileLock"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "filelock.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filelock.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filelock.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filelock.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filelock.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filelock.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filelock.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filelock.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "filelock.__version__", "name": "__version__", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "has_fcntl": {".class": "SymbolTableNode", "cross_ref": "filelock._unix.has_fcntl", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "version": {".class": "SymbolTableNode", "cross_ref": "filelock.version.version", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\filelock\\__init__.py"}