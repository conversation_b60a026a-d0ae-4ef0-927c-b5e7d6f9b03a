{"data_mtime": 1754286349, "dep_lines": [12, 2, 3, 4, 5, 1, 6, 7, 8, 9, 10, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["concurrent.futures._base", "collections.abc", "multiprocessing.connection", "multiprocessing.context", "multiprocessing.queues", "sys", "threading", "types", "typing", "typing_extensions", "weakref", "builtins", "_frozen_importlib", "_thread", "_typeshed", "abc", "multiprocessing", "multiprocessing.process"], "hash": "453634c9cad0e29c54548cd81d65b7ea1ee1a2c6", "id": "concurrent.futures.process", "ignore_all": true, "interface_hash": "8000d95652bf85cbdf126b1da11faacaf13357ab", "mtime": 1751962336, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\concurrent\\futures\\process.pyi", "plugin_data": null, "size": 8361, "suppressed": [], "version_id": "1.15.0"}