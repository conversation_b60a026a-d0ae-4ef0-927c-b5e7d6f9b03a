{".class": "MypyFile", "_fullname": "utils.buffer.reservoir_update", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Reservoir_update": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "utils.buffer.reservoir_update.Reservoir_update", "name": "Reservoir_update", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "utils.buffer.reservoir_update.Reservoir_update", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "utils.buffer.reservoir_update", "mro": ["utils.buffer.reservoir_update.Reservoir_update", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.buffer.reservoir_update.Reservoir_update.__init__", "name": "__init__", "type": null}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "buffer", "x", "y", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.buffer.reservoir_update.Reservoir_update.update", "name": "update", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "utils.buffer.reservoir_update.Reservoir_update.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "utils.buffer.reservoir_update.Reservoir_update", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.buffer.reservoir_update.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.buffer.reservoir_update.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.buffer.reservoir_update.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.buffer.reservoir_update.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.buffer.reservoir_update.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.buffer.reservoir_update.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "torch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "utils.buffer.reservoir_update.torch", "name": "torch", "type": {".class": "AnyType", "missing_import_name": "utils.buffer.reservoir_update.torch", "source_any": null, "type_of_any": 3}}}}, "path": "D:\\continual_learning\\CL_ER\\utils\\buffer\\reservoir_update.py"}