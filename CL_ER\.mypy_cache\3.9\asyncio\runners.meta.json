{"data_mtime": 1754286349, "dep_lines": [3, 8, 1, 2, 4, 5, 6, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "asyncio.events", "sys", "_typeshed", "<PERSON><PERSON><PERSON>", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc"], "hash": "b87cc7cfaa4569e4dbbac5f37bc93a9c4dbf0f5d", "id": "asyncio.runners", "ignore_all": true, "interface_hash": "8347eaa539e83cf06104cc603b51c5b37fe2136e", "mtime": 1751962336, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\asyncio\\runners.pyi", "plugin_data": null, "size": 1205, "suppressed": [], "version_id": "1.15.0"}