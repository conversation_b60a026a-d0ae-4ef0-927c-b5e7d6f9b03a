{".class": "MypyFile", "_fullname": "agents.compare_forgetting", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CompareForgetAgent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "agents.compare_forgetting.CompareForgetAgent", "name": "CompareForgetAgent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "agents.compare_forgetting.CompareForgetAgent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "agents.compare_forgetting", "mro": ["agents.compare_forgetting.CompareForgetAgent", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.compare_forgetting.CompareForgetAgent.__init__", "name": "__init__", "type": null}}, "_compare_results": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.compare_forgetting.CompareForgetAgent._compare_results", "name": "_compare_results", "type": null}}, "_visualize_results": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "save_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.compare_forgetting.CompareForgetAgent._visualize_results", "name": "_visualize_results", "type": null}}, "execute_and_compare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "forget_task_id", "subjects_to_keep", "save_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.compare_forgetting.CompareForgetAgent.execute_and_compare", "name": "execute_and_compare", "type": null}}, "register_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "agent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.compare_forgetting.CompareForgetAgent.register_method", "name": "register_method", "type": null}}, "results": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.compare_forgetting.CompareForgetAgent.results", "name": "results", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "unlearning_methods": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.compare_forgetting.CompareForgetAgent.unlearning_methods", "name": "unlearning_methods", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "agents.compare_forgetting.CompareForgetAgent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "agents.compare_forgetting.CompareForgetAgent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CompareForgettingAgent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["agents.exp_replay.ExperienceReplay"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "agents.compare_forgetting.CompareForgettingAgent", "name": "CompareForgettingAgent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "agents.compare_forgetting.CompareForgettingAgent", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "agents.compare_forgetting", "mro": ["agents.compare_forgetting.CompareForgettingAgent", "agents.exp_replay.ExperienceReplay", "agents.base.ContinualLearner", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "optimizer", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.compare_forgetting.CompareForgettingAgent.__init__", "name": "__init__", "type": null}}, "_basic_unlearning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "agent", "forget_task_id", "subjects_to_keep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.compare_forgetting.CompareForgettingAgent._basic_unlearning", "name": "_basic_unlearning", "type": null}}, "_compare_results": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "results"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.compare_forgetting.CompareForgettingAgent._compare_results", "name": "_compare_results", "type": null}}, "_create_model_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.compare_forgetting.CompareForgettingAgent._create_model_copy", "name": "_create_model_copy", "type": null}}, "_evaluate_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "forget_task_id", "subjects_to_keep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.compare_forgetting.CompareForgettingAgent._evaluate_model", "name": "_evaluate_model", "type": null}}, "_get_task_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.compare_forgetting.CompareForgettingAgent._get_task_data", "name": "_get_task_data", "type": null}}, "_print_method_results": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "method", "results"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.compare_forgetting.CompareForgettingAgent._print_method_results", "name": "_print_method_results", "type": null}}, "_run_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "method", "model", "forget_task_id", "subjects_to_keep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.compare_forgetting.CompareForgettingAgent._run_method", "name": "_run_method", "type": null}}, "available_methods": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "agents.compare_forgetting.CompareForgettingAgent.available_methods", "name": "available_methods", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "run_comparison": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "forget_task_id", "subjects_to_keep", "methods"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "agents.compare_forgetting.CompareForgettingAgent.run_comparison", "name": "run_comparison", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "agents.compare_forgetting.CompareForgettingAgent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "agents.compare_forgetting.CompareForgettingAgent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExperienceReplay": {".class": "SymbolTableNode", "cross_ref": "agents.exp_replay.ExperienceReplay", "kind": "Gdef"}, "TSNE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "agents.compare_forgetting.TSNE", "name": "TSNE", "type": {".class": "AnyType", "missing_import_name": "agents.compare_forgetting.TSNE", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.compare_forgetting.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.compare_forgetting.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.compare_forgetting.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.compare_forgetting.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.compare_forgetting.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "agents.compare_forgetting.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "auc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "agents.compare_forgetting.auc", "name": "auc", "type": {".class": "AnyType", "missing_import_name": "agents.compare_forgetting.auc", "source_any": null, "type_of_any": 3}}}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "pd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "agents.compare_forgetting.pd", "name": "pd", "type": {".class": "AnyType", "missing_import_name": "agents.compare_forgetting.pd", "source_any": null, "type_of_any": 3}}}, "plt": {".class": "SymbolTableNode", "cross_ref": "matplotlib.pyplot", "kind": "Gdef"}, "roc_curve": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "agents.compare_forgetting.roc_curve", "name": "roc_curve", "type": {".class": "AnyType", "missing_import_name": "agents.compare_forgetting.roc_curve", "source_any": null, "type_of_any": 3}}}, "sns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "agents.compare_forgetting.sns", "name": "sns", "type": {".class": "AnyType", "missing_import_name": "agents.compare_forgetting.sns", "source_any": null, "type_of_any": 3}}}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "D:\\continual_learning\\CL_ER\\agents\\compare_forgetting.py"}