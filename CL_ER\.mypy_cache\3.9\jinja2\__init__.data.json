{".class": "MypyFile", "_fullname": "jinja2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseLoader": {".class": "SymbolTableNode", "cross_ref": "jinja2.loaders.BaseLoader", "kind": "Gdef"}, "BytecodeCache": {".class": "SymbolTableNode", "cross_ref": "jinja2.bccache.BytecodeCache", "kind": "Gdef"}, "ChainableUndefined": {".class": "SymbolTableNode", "cross_ref": "jinja2.runtime.ChainableUndefined", "kind": "Gdef"}, "ChoiceLoader": {".class": "SymbolTableNode", "cross_ref": "jinja2.loaders.ChoiceLoader", "kind": "Gdef"}, "DebugUndefined": {".class": "SymbolTableNode", "cross_ref": "jinja2.runtime.DebugUndefined", "kind": "Gdef"}, "DictLoader": {".class": "SymbolTableNode", "cross_ref": "jinja2.loaders.DictLoader", "kind": "Gdef"}, "Environment": {".class": "SymbolTableNode", "cross_ref": "jinja2.environment.Environment", "kind": "Gdef"}, "FileSystemBytecodeCache": {".class": "SymbolTableNode", "cross_ref": "jinja2.bccache.FileSystemBytecodeCache", "kind": "Gdef"}, "FileSystemLoader": {".class": "SymbolTableNode", "cross_ref": "jinja2.loaders.FileSystemLoader", "kind": "Gdef"}, "FunctionLoader": {".class": "SymbolTableNode", "cross_ref": "jinja2.loaders.FunctionLoader", "kind": "Gdef"}, "MemcachedBytecodeCache": {".class": "SymbolTableNode", "cross_ref": "jinja2.bccache.MemcachedBytecodeCache", "kind": "Gdef"}, "ModuleLoader": {".class": "SymbolTableNode", "cross_ref": "jinja2.loaders.ModuleLoader", "kind": "Gdef"}, "PackageLoader": {".class": "SymbolTableNode", "cross_ref": "jinja2.loaders.PackageLoader", "kind": "Gdef"}, "PrefixLoader": {".class": "SymbolTableNode", "cross_ref": "jinja2.loaders.PrefixLoader", "kind": "Gdef"}, "StrictUndefined": {".class": "SymbolTableNode", "cross_ref": "jinja2.runtime.StrictUndefined", "kind": "Gdef"}, "Template": {".class": "SymbolTableNode", "cross_ref": "jinja2.environment.Template", "kind": "Gdef"}, "TemplateAssertionError": {".class": "SymbolTableNode", "cross_ref": "jinja2.exceptions.TemplateAssertionError", "kind": "Gdef"}, "TemplateError": {".class": "SymbolTableNode", "cross_ref": "jinja2.exceptions.TemplateError", "kind": "Gdef"}, "TemplateNotFound": {".class": "SymbolTableNode", "cross_ref": "jinja2.exceptions.TemplateNotFound", "kind": "Gdef"}, "TemplateRuntimeError": {".class": "SymbolTableNode", "cross_ref": "jinja2.exceptions.TemplateRuntimeError", "kind": "Gdef"}, "TemplateSyntaxError": {".class": "SymbolTableNode", "cross_ref": "jinja2.exceptions.TemplateSyntaxError", "kind": "Gdef"}, "TemplatesNotFound": {".class": "SymbolTableNode", "cross_ref": "jinja2.exceptions.TemplatesNotFound", "kind": "Gdef"}, "Undefined": {".class": "SymbolTableNode", "cross_ref": "jinja2.runtime.Undefined", "kind": "Gdef"}, "UndefinedError": {".class": "SymbolTableNode", "cross_ref": "jinja2.exceptions.UndefinedError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "jinja2.__version__", "name": "__version__", "type": "builtins.str"}}, "clear_caches": {".class": "SymbolTableNode", "cross_ref": "jinja2.utils.clear_caches", "kind": "Gdef"}, "is_undefined": {".class": "SymbolTableNode", "cross_ref": "jinja2.utils.is_undefined", "kind": "Gdef"}, "make_logging_undefined": {".class": "SymbolTableNode", "cross_ref": "jinja2.runtime.make_logging_undefined", "kind": "Gdef"}, "pass_context": {".class": "SymbolTableNode", "cross_ref": "jinja2.utils.pass_context", "kind": "Gdef"}, "pass_environment": {".class": "SymbolTableNode", "cross_ref": "jinja2.utils.pass_environment", "kind": "Gdef"}, "pass_eval_context": {".class": "SymbolTableNode", "cross_ref": "jinja2.utils.pass_eval_context", "kind": "Gdef"}, "select_autoescape": {".class": "SymbolTableNode", "cross_ref": "jinja2.utils.select_autoescape", "kind": "Gdef"}}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\jinja2\\__init__.py"}