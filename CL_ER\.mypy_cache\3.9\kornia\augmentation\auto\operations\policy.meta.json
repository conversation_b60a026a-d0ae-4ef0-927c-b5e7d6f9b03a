{"data_mtime": 1754286447, "dep_lines": [23, 24, 25, 26, 27, 22, 28, 29, 18, 20, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["kornia.augmentation.auto.operations", "kornia.augmentation.container.base", "kornia.augmentation.container.ops", "kornia.augmentation.container.params", "kornia.augmentation.utils", "kornia.augmentation", "kornia.core", "kornia.utils", "typing", "torch", "kornia", "builtins", "string", "torch.distributed._functional_collectives", "types", "itertools", "contextlib", "traceback", "torch.distributed", "torch.nn", "html", "operator", "os", "re", "sys", "multiprocessing.reduction", "collections", "warnings", "pprint", "inspect", "copy", "math", "uuid", "_frozen_importlib", "abc", "kornia.augmentation.auto.operations.base", "kornia.augmentation.base", "kornia.augmentation.container", "kornia.core.mixin", "kornia.core.mixin.onnx", "kornia.core.module", "torch._C", "torch._tensor", "torch.autograd", "torch.autograd.function", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module"], "hash": "8a700b0cb75a7cf1580e720b929ff40e7f8a6f78", "id": "kornia.augmentation.auto.operations.policy", "ignore_all": true, "interface_hash": "d1d67d1aef6a35f0368f9452209b69dfa7f412aa", "mtime": 1740555645, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "d:\\anaconda3\\envs\\test\\lib\\site-packages\\kornia\\augmentation\\auto\\operations\\policy.py", "plugin_data": null, "size": 5879, "suppressed": [], "version_id": "1.15.0"}