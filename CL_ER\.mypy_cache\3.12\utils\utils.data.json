{".class": "MypyFile", "_fullname": "utils.utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AverageMeter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "utils.utils.AverageMeter", "name": "AverageMeter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "utils.utils.AverageMeter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "utils.utils", "mro": ["utils.utils.AverageMeter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.utils.AverageMeter.__init__", "name": "__init__", "type": null}}, "avg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.utils.AverageMeter.avg", "name": "avg", "type": null}}, "count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.utils.AverageMeter.count", "name": "count", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.utils.AverageMeter.reset", "name": "reset", "type": null}}, "sum": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.utils.AverageMeter.sum", "name": "sum", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "val", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.utils.AverageMeter.update", "name": "update", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "utils.utils.AverageMeter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "utils.utils.AverageMeter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EarlyStopping": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "utils.utils.EarlyStopping", "name": "EarlyStopping", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "utils.utils.EarlyStopping", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "utils.utils", "mro": ["utils.utils.EarlyStopping", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "min_delta", "patience", "cumulative_delta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.utils.EarlyStopping.__init__", "name": "__init__", "type": null}}, "best_score": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.utils.EarlyStopping.best_score", "name": "best_score", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "counter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.utils.EarlyStopping.counter", "name": "counter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "cumulative_delta": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.utils.EarlyStopping.cumulative_delta", "name": "cumulative_delta", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "min_delta": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.utils.EarlyStopping.min_delta", "name": "min_delta", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "patience": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "utils.utils.EarlyStopping.patience", "name": "patience", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.utils.EarlyStopping.reset", "name": "reset", "type": null}}, "step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "score"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.utils.EarlyStopping.step", "name": "step", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "utils.utils.EarlyStopping.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "utils.utils.EarlyStopping", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "boolean_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.utils.boolean_string", "name": "boolean_string", "type": null}}, "euclidean_distance": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["u", "v"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.utils.euclidean_distance", "name": "euclidean_distance", "type": null}}, "maybe_cuda": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["what", "use_cuda", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.utils.maybe_cuda", "name": "maybe_cuda", "type": null}}, "mini_batch_deep_features": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["model", "total_x", "num"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.utils.mini_batch_deep_features", "name": "mini_batch_deep_features", "type": null}}, "nonzero_indices": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["bool_mask_tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.utils.nonzero_indices", "name": "nonzero_indices", "type": null}}, "ohe_label": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["label_tensor", "dim", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.utils.ohe_label", "name": "ohe_label", "type": null}}, "torch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "utils.utils.torch", "name": "torch", "type": {".class": "AnyType", "missing_import_name": "utils.utils.torch", "source_any": null, "type_of_any": 3}}}}, "path": "D:\\continual_learning\\CL_ER\\utils\\utils.py"}