{"data_mtime": 1754286450, "dep_lines": [2, 4, 5, 2, 4, 6, 7, 8, 9, 10, 1, 3, 12, 13, 14, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 11], "dep_prios": [10, 10, 5, 20, 20, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["torch.nn.functional", "torch.utils.data", "utils.buffer.buffer", "torch.nn", "torch.utils", "agents.base", "continuum.data_utils", "utils.setup_elements", "utils.utils", "agents.prototype_anchored_er", "torch", "numpy", "random", "copy", "os", "datetime", "builtins", "string", "torch.distributed._functional_collectives", "types", "itertools", "contextlib", "traceback", "torch.distributed", "html", "operator", "re", "sys", "typing", "multiprocessing.reduction", "collections", "warnings", "pprint", "inspect", "math", "uuid", "_frozen_importlib", "_typeshed", "abc", "agents.exp_replay", "continuum", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.nn.modules", "torch.nn.modules.module", "torch.optim", "torch.optim.optimizer", "torch.optim.sgd", "torch.return_types", "torch.utils._contextlib", "torch.utils.data.dataloader", "torch.utils.data.dataset", "torch.utils.data.sampler", "utils"], "hash": "2d112a46359f3f110682bf084391eec2184f5aeb", "id": "agents.PAMU_ER", "ignore_all": true, "interface_hash": "3c8adca67e2869e6de291579f7ede6fb64e09d4d", "mtime": 1751895222, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\continual_learning\\CL_ER\\agents\\PAMU_ER.py", "plugin_data": null, "size": 30561, "suppressed": ["sklearn.metrics"], "version_id": "1.15.0"}